import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { signInStart, signInSuccess, signInFailure } from "../redux/user/userSlice";

const SignIn = () => {
  const [formData, setFormData] = useState({});
  const { loading, error } = useSelector((state) => state.user);
  const navigate = useNavigate();
  const dispatch = useDispatch(); 

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.id]: e.target.value,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault(); //prevent refresh the page when submit
    try {
      dispatch(signInStart());
      
      // const res = await fetch(`${import.meta.env.VITE_API_IP}/v2/auth/login`, {
      // Updated to use the new /auth/login endpoint
      const res = await fetch(`${import.meta.env.VITE_API_IP}/auth/login`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: 'include',
        body: JSON.stringify(formData),
      });
      
      const data = await res.json();
      
      if (data.success === false) {
        dispatch(signInFailure(data.message));
        return;
      }
      dispatch(signInSuccess(data));
      navigate("/adminpanel");
    } catch (error) {
      dispatch(signInFailure(error.message));
    }
  };


  return (
    <div className="p-6 max-w-lg mx-auto bg-gray-50 min-h-screen mt-16">
      <form onSubmit={handleSubmit} className="flex flex-col gap-4 bg-white p-6 rounded-lg shadow-md">
      <h1 className="text-3xl text-center font-semibold my-7 text-gray-800">Sign In</h1>
        <input
          type="text"
          placeholder="Username"
          className="p-3 rounded-lg bg-gray-100 border border-gray-300"
          id="username"
          onChange={handleChange}
        />
        <input
          type="password"
          placeholder="Password"
          className="p-3 rounded-lg bg-gray-100 border border-gray-300"
          id="password"
          onChange={handleChange}
        />
        <button
          disabled={loading}
          className="bg-blue-600 text-white p-3 rounded-lg uppercase hover:bg-blue-700 disabled:opacity-50"
        >
          {loading ? "Loading..." : "Sign In"}
        </button>
        {/* <OAuth /> */}
      </form>
      {/* <div className="flex gap-2 mt-3 text-sm text-gray-700">
        <p>Do not have an account?</p>
        <Link to="/sign-up">
          <span className="text-blue-600 hover:underline">Sign Up</span>
        </Link>
      </div> */}
      {error && <p className="text-red-600 mt-5">{error}</p>}
    </div>
  );
};

export default SignIn;
