import PropTypes from 'prop-types';
import { FaEdit, FaTrash } from 'react-icons/fa';

const formatKey = (key) => {
    const keyMap = {
        'our_journey': 'Our Journey',
        'highlights': 'Highlights',
        'sustainability': 'Sustainability',
        'dark_days': 'Dark Days'
    };
    return keyMap[key] || key;
};

const getKeyStyles = (key) => {
    const styleMap = {
        'our_journey': 'bg-purple-100 text-purple-600',
        'highlights': 'bg-orange-100 text-orange-600',
        'sustainability': 'bg-green-100 text-green-600',
        'dark_days': 'bg-red-100 text-red-600'
    };
    return styleMap[key] || 'bg-gray-100 text-gray-600';
};

const TimelineCard = ({ timeline, onEdit, onDelete }) => {
    return (
        <div className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 flex flex-col h-full">
            <div className="relative">
                <img
                    src={`${import.meta.env.VITE_API_IP}/uploads/${timeline.thumbnail}`}
                    alt={timeline.title}
                    className="w-full h-48 object-cover"
                    onError={(e) => {
                        console.error('Image failed to load:', e);
                        console.log('Attempted URL:', e.target.src);
                    }}
                />
            </div>
            <div className="p-4 flex flex-col flex-grow">
                <div className="mb-2">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getKeyStyles(timeline.key)}`}>
                        {formatKey(timeline.key)}
                    </span>
                </div>
                <div className="group relative">
                    <h2 className="text-xl font-semibold text-gray-800 mb-2 truncate">
                        {timeline.title}
                    </h2>
                    <div className="absolute left-0 w-full bg-gray-900 text-white text-sm rounded-md py-1 px-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10 -top-8">
                        {timeline.title}
                    </div>
                </div>
                <span className="text-sm text-gray-500 font-medium">
                    Year: {timeline.year}
                </span>
                <div className="flex-grow"></div>
                <div className="flex justify-between items-center mt-4 pt-2 border-t border-gray-100">
                    <button
                        onClick={() => onEdit(timeline._id)}
                        className="p-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors duration-200 flex items-center gap-1"
                        aria-label="Edit timeline"
                    >
                        <FaEdit className="w-4 h-4" />
                    </button>
                    <button
                        onClick={() => onDelete(timeline._id)}
                        className="p-2 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors duration-200 flex items-center gap-1"
                        aria-label="Delete timeline"
                    >
                        <FaTrash className="w-4 h-4" />
                    </button>
                </div>
            </div>
        </div>
    );
};

// Define prop types
TimelineCard.propTypes = {
    timeline: PropTypes.shape({
        _id: PropTypes.string.isRequired,
        thumbnail: PropTypes.string.isRequired,
        title: PropTypes.string.isRequired,
        key: PropTypes.string.isRequired,
        year: PropTypes.number.isRequired,
    }).isRequired,
    onEdit: PropTypes.func.isRequired,
    onDelete: PropTypes.func.isRequired,
};

export default TimelineCard;
