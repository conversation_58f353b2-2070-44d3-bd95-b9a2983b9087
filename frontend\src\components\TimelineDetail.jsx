import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { get } from '../utils/api';

const TimelineDetail = () => {
    const { id } = useParams();
    const [timeline, setTimeline] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const fetchTimeline = async () => {
            try {
                const response = await get(`${import.meta.env.VITE_API_IP}/v2/timeline/${id}`);
                const data = await response.json();
                if (data.success) {
                    setTimeline(data.timeline); // Assuming the response contains the timeline object
                } else {
                    setError(data.message);
                }
            } catch (err) {
                setError(err.message);
            } finally {
                setLoading(false);
            }
        };

        fetchTimeline();
    }, [id]);

    if (loading) return <p>Loading...</p>;
    if (error) return <p className="text-red-600">{error}</p>;

    return (
        <div className="max-w-4xl mx-auto p-6">
            <h1 className="text-3xl font-semibold">{timeline.title}</h1>
            <img src={timeline.thumbnail} alt={timeline.title} className="w-full h-64 object-cover" />
            <p className="mt-4"><strong>Category:</strong> {timeline.key}</p>
            <p className="mt-2"><strong>Year:</strong> {timeline.year}</p>
            <p className="mt-2"><strong>Description:</strong> {timeline.description}</p>
            <p className="mt-2"><strong>Caption:</strong> {timeline.caption}</p>
            {/* Display other files if needed */}
        </div>
    );
};

export default TimelineDetail;
