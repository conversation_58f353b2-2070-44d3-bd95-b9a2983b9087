import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react-swc'
import tailwindcss from '@tailwindcss/vite'
import { dirname } from 'path'
import { fileURLToPath } from 'url'

const __dirname = dirname(fileURLToPath(import.meta.url))

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, __dirname, '')

  console.log("🔧 Loaded VITE_API_IP:", env.VITE_API_IP);

  // const isProduction = mode === 'production'

  return {
    base: '/milestone-v2/',
    server: {
      proxy: {
        '/v2': {
          target: env.VITE_API_IP,
          secure: false,
          changeOrigin: true
        },
        '/keymilestone': {
          target: env.VITE_API_IP,
          secure: false,
          changeOrigin: true
        },
        '/uploads': {
          target: env.VITE_API_IP,
          secure: false,
          changeOrigin: true
        }
      }
    },
    plugins: [
      react(),
      tailwindcss(),
    ],
  }
})
