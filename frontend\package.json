{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@headlessui/react": "^2.2.1", "@heroicons/react": "^2.2.0", "@react-three/drei": "^10.0.6", "@react-three/fiber": "^9.1.2", "@react-three/postprocessing": "^3.0.4", "@reduxjs/toolkit": "^2.5.1", "@tailwindcss/vite": "^4.0.8", "firebase": "^11.3.1", "framer-motion": "^12.6.5", "gsap": "^3.12.7", "noisejs": "^2.1.0", "prop-types": "^15.8.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.1.5", "react-toastify": "^11.0.5", "redux-persist": "^6.0.0", "simplex-noise": "^4.0.3", "three": "^0.175.0"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "dotenv": "^16.5.0", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.5.1", "tailwindcss": "^4.0.8", "vite": "^6.2.5"}}