import jwt from 'jsonwebtoken';
import { errorHandler } from './error.js'; // Ensure this path is correct

// export const verifyToken = (req, res, next) => {
//     const token = req.cookies.access_token;
//     if (!token) return next(errorHandler(403, "You are not authenticated!"));

//     jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
//         if (err) return next(errorHandler(403, "Token is not valid!"));
//         req.user = user;
//         next();
//     });
// }; 

export const verifyToken = (req, res, next) => {
    let token = req.cookies.access_token;

    // Fallback: check Authorization header
    if (!token && req.headers.authorization?.startsWith('Bearer ')) {
        token = req.headers.authorization.split(' ')[1];
    }

    if (!token) return next(errorHandler(403, "You are not authenticated!"));

    jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
        if (err) return next(errorHand<PERSON>(403, "Token is not valid!"));
        req.user = user;
        next();
    });
};