import mongoose from "mongoose";

const timelineSchema = new mongoose.Schema({
    year: {
        type: Number,
        required: true,
    },
    key: {
        type: String,
        enum: ["our_journey", "highlights", "sustainability", "dark_days"],
        default: "our_journey",
    },
    title: {
        type: String,
        required: true,
    },
    thumbnail: {
        type: String,
    },
    description: {
        type: String,
        required: true,
    },
    caption: {
        type: String,
    },
    files: [{
        path: {
            type: String,
            required: true,
        },
        type: {
            type: String,
            enum: ["image", "video"],
            required: true,
        },
        name: {
            type: String,
            required: true,
        },
        size: {
            type: Number,
            required: true,
        },
        mimeType: {
            type: String,
            required: true,
        },
        uploadedAt: {
            type: Date,
            default: Date.now,
        }
    }],
}, { timestamps: true });

const Timeline = mongoose.model('Timeline', timelineSchema);

export default Timeline; 