import { store } from '../redux/store';

/**
 * Helper function to prepare headers with authentication
 * @returns {Object} Headers object with auth token if available
 */
export const getAuthHeaders = () => {
  const headers = {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  };
  
  const state = store.getState();
  const token = state.user?.currentUser?.token;
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  return headers;
};

/**
 * Performs an authenticated fetch request
 * @param {string} url - The URL to fetch
 * @param {Object} options - Fetch options
 * @returns {Promise} - Fetch promise
 */
export const fetchWithAuth = async (url, options = {}) => {
  const headers = options.headers || {};
  const authHeaders = getAuthHeaders();
  
  // Don't override Content-Type if multipart/form-data (for file uploads)
  if (options.body instanceof FormData) {
    delete authHeaders['Content-Type'];
  }
  
  const mergedOptions = {
    ...options,
    headers: { ...authHeaders, ...headers },
    credentials: 'include' // Always include credentials
  };
  
  return fetch(url, mergedOptions);
};

/**
 * Get request with authentication
 * @param {string} url - The URL to fetch
 * @returns {Promise} - Fetch promise
 */
export const get = (url) => {
  return fetchWithAuth(url);
};

/**
 * Post request with authentication
 * @param {string} url - The URL to post to
 * @param {Object|FormData} data - The data to send
 * @returns {Promise} - Fetch promise
 */
export const post = (url, data) => {
  const options = {
    method: 'POST'
  };
  
  if (data instanceof FormData) {
    options.body = data;
  } else {
    options.body = JSON.stringify(data);
  }
  
  return fetchWithAuth(url, options);
};

/**
 * Put request with authentication
 * @param {string} url - The URL to put to
 * @param {Object|FormData} data - The data to send
 * @returns {Promise} - Fetch promise
 */
export const put = (url, data) => {
  const options = {
    method: 'PUT'
  };
  
  if (data instanceof FormData) {
    options.body = data;
  } else {
    options.body = JSON.stringify(data);
  }
  
  return fetchWithAuth(url, options);
};

/**
 * Delete request with authentication
 * @param {string} url - The URL to delete
 * @returns {Promise} - Fetch promise
 */
export const del = (url) => {
  return fetchWithAuth(url, { method: 'DELETE' });
}; 