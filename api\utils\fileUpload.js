import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { errorHand<PERSON> } from './error.js';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Define the upload directory for testing
// const UPLOAD_DIR = path.join(__dirname, '../../uploads'); // Change this to your desired local path

// Comment out the original upload directory for server
const UPLOAD_DIR = '/home/<USER>/backend-deploy/uploads';
// const UPLOAD_DIR = '/var/www/html/uploads';

// Ensure the upload directory exists
if (!fs.existsSync(UPLOAD_DIR)) {
    fs.mkdirSync(UPLOAD_DIR, { recursive: true });
}

// Configure storage
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, UPLOAD_DIR);
    },
    filename: function (req, file, cb) {
        // Generate a unique filename with timestamp
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, uniqueSuffix + path.extname(file.originalname));
    }
});

// File filter to accept only images and videos
const fileFilter = (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'video/mp4', 'video/quicktime'];
    
    if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
    } else {
        cb(new Error('Invalid file type. Only images and videos are allowed.'), false);
    }
};

// Configure multer
export const upload = multer({
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 50 * 1024 * 1024, // 50MB limit
    }
});

// Function to handle multiple file uploads
export const handleFileUpload = async (req, res, next) => {
    try {
        if (!req.files || req.files.length === 0) {
            return next(errorHandler(400, 'No files were uploaded'));
        }

        const uploadedFiles = req.files.map(file => ({
            path: file.path,
            type: file.mimetype.startsWith('image/') ? 'image' : 'video',
            name: file.originalname,
            size: file.size,
            mimeType: file.mimetype
        }));

        return uploadedFiles;
    } catch (error) {
        next(error);
    }
};

// Function to delete files
export const deleteFiles = async (filePaths) => {
    try {
        for (const filePath of filePaths) {
            if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
            }
        }
    } catch (error) {
        console.error('Error deleting files:', error);
        throw error;
    }
}; 