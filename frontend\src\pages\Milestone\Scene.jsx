/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-unused-vars */
import { useMemo, useEffect, useRef, useState } from "react";
import { Canvas } from "@react-three/fiber";
import { Html } from "@react-three/drei";
import { Bloom, EffectComposer } from "@react-three/postprocessing";
import PopupCards from "../../components/Milestone/PopupCards";
import LandingPage from "./LandingPage";
import '/src/assets/fonts/fonts.css';
import { BackGround } from "../../components/Milestone/BackGround";
import { CameraControls } from "../../components/Milestone/CameraControls";
import { Particles } from "../../components/Milestone/Particles";
import { FloatingParticles } from "../../components/Milestone/FloatingParticles";
import BigParticles from "../../components/Milestone/BigParticles";

export const Scene = () => {
  const [timelines, setTimelines] = useState([]);
  const [error, setError] = useState(null);
  const [openCards, setOpenCards] = useState([]);
  const [activeCategory, setActiveCategory] = useState("All");
  const cameraRef = useRef(null);
  const [currentYear, setCurrentYear] = useState(null);
  const inactivityTimerRef = useRef(null);
  const INACTIVITY_TIMEOUT = 5 * 60 * 1000;
  const [isPlaying, setIsPlaying] = useState(false);
  const audioRef = useRef(null);
  const [showInfoPopup, setShowInfoPopup] = useState(false);
  const [isInitialAnimationDone, setIsInitialAnimationDone] = useState(false);
  const [showLanding, setShowLanding] = useState(true);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [startWaveAnimation, setStartWaveAnimation] = useState(false);
  const [isWaveAnimationComplete, setIsWaveAnimationComplete] = useState(false);

  const categories = [
    { label: "All", color: "#2C2DA3" },
    { label: "Our Journey", color: "#6D0CE7", key: "our_journey" },
    { label: "Highlights", color: "#F49C53", key: "highlights" },
    { label: "Sustainability", color: "#32D5B3", key: "sustainability" },
    { label: "Dark Days", color: "#FD1030", key: "dark_days" },
  ];

  const [color, setColor] = useState("#000000");
  const [isActive, setActive] = useState(false);

  const bloomSettings = useMemo(() => {
    const isHighRes = window.innerWidth >= 1440;
    return {
      intensity: isHighRes ? 0.3 : 0.5,
      luminanceThreshold: isHighRes ? 0.9 : 0.8,
    };
  }, []);

  const toggleAudio = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play().catch((err) => console.error("Audio play failed:", err));
      }
      setIsPlaying(!isPlaying);
    }
  };

  const resetInactivityTimer = () => {
    if (inactivityTimerRef.current) {
      clearTimeout(inactivityTimerRef.current);
    }
    inactivityTimerRef.current = setTimeout(() => {
      setIsTransitioning(true);
      setTimeout(() => {
        setShowLanding(true);
        setActiveCategory("All");
        setOpenCards([]);
        setColor("none");
        setActive(false);
        setTimelines([]);
        setError(null);
        setCurrentYear(null);
        setIsPlaying(false);
        setShowInfoPopup(false);
        setIsInitialAnimationDone(false);
        setIsTransitioning(false);
        setStartWaveAnimation(false);
        setIsWaveAnimationComplete(false);

        if (audioRef.current) {
          audioRef.current.pause();
          audioRef.current.currentTime = 0;
        }

        if (cameraRef.current && timelines.length > 0) {
          cameraRef.current(timelines.length - 1);
        }
      }, 500);
    }, INACTIVITY_TIMEOUT);
  };

  const startExperience = () => {
    // console.log("startExperience called");
    setIsTransitioning(true);
    if (audioRef.current) {
      audioRef.current.play()
        .then(() => {
          setIsPlaying(true);
        })
        .catch((err) => {
          console.error("Audio play failed:", err);
          setIsPlaying(false);
        });
    }

    // Reset animation states before starting new animation
    setIsInitialAnimationDone(false);
    setIsWaveAnimationComplete(false);

    // Start wave animation immediately
    // console.log("Setting startWaveAnimation to true");
    setStartWaveAnimation(true);
    setShowLanding(false);
    setTimeout(() => {
      setIsTransitioning(false);
    }, 500);

    // Fetch data asynchronously
    // console.log("Fetching timeline data");
    fetch(`${import.meta.env.VITE_API_IP}/v2/timeline/public`)
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          // console.log("Timeline data fetched successfully", { count: data.timelines.length });
          const sortedTimelines = data.timelines.sort((a, b) => a.year - b.year);
          setTimelines(sortedTimelines);
        } else {
          setError(data.message);
        }
      })
      .catch((err) => {
        console.error("Error fetching timeline data:", err);
        setError(err.message);
      });
    resetInactivityTimer();
  };

  const handleWaveAnimationComplete = (timelinesLength) => {
    // console.log("Wave animation complete called", {
    //   hasCameraRef: !!cameraRef.current,
    //   isInitialAnimationDone,
    //   timelinesLength,
    //   actualTimelinesLength: timelines.length
    // });

    // Simply mark the wave animation as complete
    // We'll handle camera movement in a separate effect that waits for both
    // the wave animation to complete AND the timeline data to be loaded
    setIsWaveAnimationComplete(true);
  };

  useEffect(() => {
    const handleActivity = () => {
      if (!showLanding) resetInactivityTimer();
    };
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    activityEvents.forEach((event) => {
      window.addEventListener(event, handleActivity);
    });
    if (!showLanding) resetInactivityTimer();
    return () => {
      activityEvents.forEach((event) => {
        window.removeEventListener(event, handleActivity);
      });
      if (inactivityTimerRef.current) {
        clearTimeout(inactivityTimerRef.current);
      }
    };
  }, [showLanding]);

  useEffect(() => {
    audioRef.current = new Audio("/milestone-v2/audio-wave.mp3");
    audioRef.current.loop = true;
    audioRef.current.volume = 0.5;
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
    };
  }, []);

  useEffect(() => {
    if (showLanding && audioRef.current && isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    }
  }, [showLanding]);

  // This effect handles camera movement when both wave animation is complete and timeline data is loaded
  useEffect(() => {
    // console.log("Checking conditions for camera movement", {
    //   isWaveAnimationComplete,
    //   timelinesLength: timelines.length,
    //   isInitialAnimationDone,
    //   hasCameraRef: !!cameraRef.current
    // });

    // Only proceed if wave animation is complete, we have timeline data, and camera hasn't been moved yet
    if (isWaveAnimationComplete && timelines.length > 0 && !isInitialAnimationDone && cameraRef.current) {
      // console.log("All conditions met, moving camera to last timeline");

      // Add a small delay to ensure everything is ready
      setTimeout(() => {
        try {
          // Move to the last timeline item (same as "All" category)
          cameraRef.current(timelines.length - 1);
          // console.log("Camera moved to last timeline successfully");
          setIsInitialAnimationDone(true);
        } catch (error) {
          console.error("Error moving camera:", error);
        }
      }, 500);
    }
  }, [isWaveAnimationComplete, timelines.length, isInitialAnimationDone, cameraRef]);

  const filteredTimelines = useMemo(() => {
    if (activeCategory === "All") {
      return timelines;
    }
    const categoryKey = categories.find((cat) => cat.label === activeCategory)?.key;
    return timelines.filter((timeline) => timeline.key === categoryKey);
  }, [timelines, activeCategory]);

  const handleCategoryClick = (category) => {
    setActive(true);
    setColor(category.color);
    setActiveCategory(category.label);
    setOpenCards([]);
    resetInactivityTimer();
    if (category.label === "All") {
      setColor("none");
      setActive(false);
      if (cameraRef.current && timelines.length > 0) {
        cameraRef.current(timelines.length - 1);
      }
      return;
    }
    const categoryKey = category.key;
    const timelinesInCategory = timelines.filter((timeline) => timeline.key === categoryKey);
    if (timelinesInCategory.length > 0 && cameraRef.current) {
      // Sort the timelines in the category by year (ascending)
      const sortedTimelines = [...timelinesInCategory].sort((a, b) => a.year - b.year);

      // Find the index of the FIRST (earliest) year in the category
      const firstYearTimeline = sortedTimelines[0];
      const firstIndex = timelines.findIndex(
        (timeline) => timeline._id === firstYearTimeline._id
      );

      // Since the timeline items are positioned in reverse order in the 3D space,
      // we need to use the reversed index for navigation
      const reversedIndex = timelines.length - 2.7 - firstIndex; //adjust for the category navigation

      // Ensure the index is valid before navigating
      if (reversedIndex >= 0 && reversedIndex < timelines.length) {
        // Use setTimeout to ensure the camera navigation happens after the state updates
        setTimeout(() => {
          cameraRef.current(reversedIndex);
        }, 0);
      }
    }
  };

  const preventZoom = (e) => {
    if (e.touches && e.touches.length > 1) {
      e.preventDefault();
    }
    if (e.type === 'wheel' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
    }
  };

  const preventGestureZoom = (e) => {
    e.preventDefault();
  };

  useEffect(() => {
    document.addEventListener("touchmove", preventZoom, { passive: false });
    document.addEventListener("wheel", preventZoom, { passive: false });
    document.addEventListener("gesturestart", preventGestureZoom, { passive: false });
    document.addEventListener("gesturechange", preventGestureZoom, { passive: false });
    document.addEventListener("gestureend", preventGestureZoom, { passive: false });

    let lastTouchEnd = 0;
    const preventDoubleTap = (e) => {
      const now = Date.now();
      if (now - lastTouchEnd <= 300) {
        e.preventDefault();
      }
      lastTouchEnd = now;
    };
    document.addEventListener("touchend", preventDoubleTap, { passive: false });

    return () => {
      document.removeEventListener("touchmove", preventZoom);
      document.removeEventListener("wheel", preventZoom);
      document.removeEventListener("gesturestart", preventGestureZoom);
      document.removeEventListener("gesturechange", preventGestureZoom);
      document.removeEventListener("gestureend", preventGestureZoom);
      document.removeEventListener("touchend", preventDoubleTap);
    };
  }, []);

  if (error) {
    return (
      <div className="w-full h-screen bg-[#181818] flex justify-center items-center">
        <p className="text-red-600 font-medium">{error}</p>
      </div>
    );
  }

  return (
    <>
      {showLanding && (
        <div
          className="fixed inset-0 z-50 transition-opacity duration-500"
          style={{ opacity: isTransitioning ? 0 : 1 }}
        >
          <LandingPage onStart={startExperience} />
        </div>
      )}
      <div
        className="w-full h-screen relative transition-opacity duration-500"
        style={{ touchAction: "none", opacity: 1 }}
      >
        <button
          onClick={() => setShowInfoPopup(true)}
          className="absolute bottom-8 left-80 w-10 h-10 rounded-full flex items-center justify-center transition-all z-40"
          aria-label="Show information"
        >
          <img
            src="/milestone-v2/icons/information.png"
            alt="Information"
            className="w-9 h-9"
          />
        </button>
        {showInfoPopup && (
          <div
            className="fixed inset-0 flex items-center justify-center z-45"
            style={{ backgroundColor: 'rgba(0, 0, 0, 0.8)' }}
          >
            <div className="relative flex items-center justify-center w-full h-full">
              <button
                onClick={() => setShowInfoPopup(false)}
                className="absolute top-4 right-4 w-8 h-8 rounded-full flex items-center justify-center bg-gray-700 hover:bg-gray-600 transition-all"
                aria-label="Close popup"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={2}
                  stroke="white"
                  className="w-5 h-5"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
              <div className="flex gap-1 items-center">
                <div className="flex flex-col items-center">
                  <img
                    src="/milestone-v2/icons/move.gif"
                    alt="Swipe to navigate"
                    className="w-140 h-110"
                  />
                </div>
                <div className="flex flex-col items-center">
                  <img
                    src="/milestone-v2/icons/tap.gif"
                    alt="Tap to expand"
                    className="w-140 h-110"
                  />
                </div>
              </div>
            </div>
          </div>
        )}
        <div className="absolute bottom-10 left-1/2 -translate-x-1/2 flex gap-4 z-40 font-semibold">
          {categories.map((category, i) => (
            <div
              className={`flex gap-2 items-center cursor-pointer ${activeCategory === category.label ? 'opacity-100' : 'opacity-60'}`}
              key={`category-${i}`}
              onClick={() => handleCategoryClick(category)}
            >
              <div className="w-6 h-6 rounded-full border-2 border-white" style={{ backgroundColor: category.color }} />
              <span className="text-white">{category.label}</span>
            </div>
          ))}
        </div>
        <button
          onClick={toggleAudio}
          className="absolute bottom-8.5 right-80 w-10 h-10 rounded-full flex items-center justify-center transition-all z-40"
          aria-label={isPlaying ? "Pause music" : "Play music"}
        >
          {isPlaying ? (
            <img src="/milestone-v2/icons/play.png" alt="Pause" className="w-9 h-9" />
          ) : (
            <img src="/milestone-v2/icons/mute.png" alt="Play" className="w-9 h-9" />
          )}
        </button>
        <Canvas
          gl={{ antialias: true, pixelRatio: Math.min(window.devicePixelRatio, window.innerWidth >= 1440 ? 1.5 : 2) }}
          camera={{
            position: [Math.max(timelines.length, 1) / 16, 0, -1],
            zoom: 2.0,
          }}
          className="w-full h-full z-10"
          style={{ position: "absolute", top: 0, left: 0, right: 0, bottom: 0 }}
        >
          <FloatingParticles />
          <BigParticles />
          <BackGround timelinesLength={timelines.length} currentYear={currentYear} />
          <Particles
            lineColor={color}
            lineActive={isActive}
            timelines={timelines}
            visibleTimelines={filteredTimelines}
            openCards={openCards}
            setOpenCards={setOpenCards}
            setCameraRef={(ref) => (cameraRef.current = ref)}
            onYearChange={setCurrentYear}
            onWaveAnimationComplete={handleWaveAnimationComplete}
            startWaveAnimation={startWaveAnimation}
          />
          <CameraControls
            timelinesLength={Math.max(timelines.length, 1)}
            cameraRef={(ref) => (cameraRef.current = ref)}
          />
          <EffectComposer>
            <Bloom intensity={bloomSettings.intensity} luminanceThreshold={bloomSettings.luminanceThreshold} />
          </EffectComposer>
        </Canvas>
        <PopupCards
          timelines={timelines}
          openCards={openCards}
          setOpenCards={(newCards) => {
            // Ensure the maximum is 3 cards and they have unique positions
            let updatedCards = [...newCards];
            if (updatedCards.length > 3) {
              updatedCards = updatedCards.slice(-3);
            }
            
            // Check for duplicate positions that need to be fixed
            const positions = updatedCards.map(card => card.position);
            const uniquePositions = [...new Set(positions)];
            
            // Only fix duplicate positions, but don't reposition existing cards
            if (uniquePositions.length < positions.length) {
              // First, identify which cards have duplicate positions
              const positionCounts = {};
              positions.forEach(pos => {
                positionCounts[pos] = (positionCounts[pos] || 0) + 1;
              });
              
              // Find positions that appear multiple times
              const duplicatePositions = Object.keys(positionCounts).filter(
                pos => positionCounts[pos] > 1
              );
              
              // Find available positions
              const allPositions = ["left", "center", "right"];
              const availablePositions = allPositions.filter(
                pos => !uniquePositions.includes(pos)
              );
              
              // Process cards to fix duplicates, respecting original positions when possible
              const processedPositions = {};
              
              updatedCards = updatedCards.map(card => {
                // If this card's position doesn't have duplicates, keep it as is
                if (!duplicatePositions.includes(card.position)) {
                  return card;
                }
                
                // If we've seen this position before, we need to reassign it
                if (processedPositions[card.position]) {
                  // For assigning a new position, use proximity-based logic
                  let positionPriority;
                  if (card.position === "left") {
                    positionPriority = ["left", "center", "right"];
                  } else if (card.position === "right") {
                    positionPriority = ["right", "center", "left"];
                  } else {
                    positionPriority = ["center", "left", "right"];
                  }
                  
                  // Find the first available position based on priority
                  const newPosition = availablePositions.shift() || card.position;
                  return { ...card, position: newPosition };
                }
                
                // Mark this position as processed
                processedPositions[card.position] = true;
                return card;
              });
            }
            
            setOpenCards(updatedCards);
            resetInactivityTimer();
          }}
        />
      </div>
    </>
  );
};