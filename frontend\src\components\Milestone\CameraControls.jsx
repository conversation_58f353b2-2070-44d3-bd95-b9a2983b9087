/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
import { useRef, useEffect, useState } from "react";
import * as THREE from "three";
import { useThree, useFrame } from "@react-three/fiber";

/**
 * CameraControls Component
 * Handles camera movement and interaction for a 3D timeline view
 * @param {number} timelinesLength - The total number of timeline items
 * @param {function} cameraRef - Callback function to control camera externally
 */
export const CameraControls = ({ timelinesLength, cameraRef }) => {
  // Core Three.js and state setup
  const { camera } = useThree();
  const [movementDir, setMovementDir] = useState(0);
  const [mouse, setMouse] = useState(null);
  const [touch, setTouch] = useState(null);

  // Reference values for camera movement and interaction
  const target = useRef(new THREE.Vector3());
  const touchStartX = useRef(0);
  const initialTouchX = useRef(0);
  const initialMouseX = useRef(0);
  const isBouncingBack = useRef(false);
  const bounceTarget = useRef(0);
  const bounceStrength = useRef(0);
  const lastPosition = useRef(0);
  const isUserInteracting = useRef(false);
  const momentum = useRef(0);
  const lastMovementTime = useRef(0);
  const dragDistance = useRef(0);
  const scrollSpeed = useRef(0);
  const lastTouchX = useRef(0);
  const lastTouchTime = useRef(0);
  const currentDirection = useRef(0);

  // Constants for movement and interaction behavior
  const maxSpeed = 8;
  const speedIncrement = 0.1;
  const fastDecrement = 0.3;
  const dragThreshold = 20;

  // Utility function for linear interpolation
  const lerp = (a, b, t) => a + t * (b - a);

  // Camera boundary and movement constants
  const effectiveLength = Math.max(timelinesLength, 1);
  const CAMERA_MAX = effectiveLength * 0.235;
  const CAMERA_MIN = -0.3;
  const BOUNCE_MARGIN = 0.2;
  const MOMENTUM_DECAY = 0.85;
  const TILT_RETURN_SPEED = 0.7;

  /**
   * Handles mouse movement for camera control
   * Updates momentum and scroll speed based on mouse movement
   */
  const handleMouseMove = (e) => {
    const now = performance.now();
    const timeDelta = now - lastMovementTime.current;

    if (e.buttons > 0 && timeDelta > 0 && e.movementX !== undefined) {
      const rawMomentum = e.movementX / timeDelta * 4;
      momentum.current = Math.sign(rawMomentum) * Math.max(Math.abs(rawMomentum), 0.1);

      if (initialMouseX.current === 0) {
        initialMouseX.current = e.clientX;
        dragDistance.current = 0;
        currentDirection.current = 0;
      } else {
        const distanceFromInitial = e.clientX - initialMouseX.current;
        const newDirection = Math.sign(distanceFromInitial);
        const absDistance = Math.abs(distanceFromInitial);
        let targetSpeedLevel;

        const isOppositeDirection = currentDirection.current !== 0 && newDirection !== 0 && newDirection !== currentDirection.current;

        if (isOppositeDirection) {
          targetSpeedLevel = Math.min(maxSpeed * 0.5, Math.pow(absDistance / (dragThreshold * 0.5), 0.5));
        } else if (absDistance < dragThreshold * 0.5) {
          targetSpeedLevel = Math.min(1.5, Math.pow(absDistance / dragThreshold, 1.8));
        } else {
          targetSpeedLevel = Math.min(maxSpeed, Math.pow(absDistance / dragThreshold, 0.7));
        }
        const targetSpeed = newDirection * targetSpeedLevel;

        const isReversing = currentDirection.current !== 0 && newDirection !== 0 && newDirection !== currentDirection.current;
        const isDecreasingSpeed = newDirection === currentDirection.current && Math.abs(targetSpeed) < Math.abs(scrollSpeed.current);
        const increment = isReversing ? fastDecrement * 2 : isDecreasingSpeed ? fastDecrement : speedIncrement;

        if (newDirection !== 0 && (currentDirection.current === 0 || newDirection === currentDirection.current || absDistance > dragThreshold)) {
          currentDirection.current = newDirection;
          scrollSpeed.current = lerp(scrollSpeed.current, targetSpeed, increment);
        }

        dragDistance.current = distanceFromInitial;
      }
    }

    lastMovementTime.current = now;
    setMouse(e);
  };

  /**
   * Initializes touch interaction
   * Sets up initial touch position and resets movement values
   */
  const handleTouchStart = (e) => {
    isUserInteracting.current = true;
    touchStartX.current = e.touches[0].clientX;
    initialTouchX.current = e.touches[0].clientX;
    lastTouchX.current = e.touches[0].clientX;
    lastTouchTime.current = performance.now();
    dragDistance.current = 0;
    scrollSpeed.current = 0;
    currentDirection.current = 0;
    lastMovementTime.current = performance.now();
    setTouch({ active: true, movementX: 0 });
  };

  /**
   * Handles touch movement for camera control
   * Updates camera position and movement based on touch gestures
   */
  const handleTouchMove = (e) => {
    const touchX = e.touches[0].clientX;
    const movementX = touchX - touchStartX.current;
    const now = performance.now();
    const timeDelta = now - lastMovementTime.current;

    // Calculate distance from initial touch position
    const distanceFromInitial = touchX - initialTouchX.current;

    // Calculate velocity (pixels per second)
    const velocity = timeDelta > 0 ? (touchX - lastTouchX.current) / (now - lastTouchTime.current) * 1000 : 0;
    const absVelocity = Math.abs(velocity);

    // Determine direction and update current direction
    // Invert direction: left swipe (negative distance) → positive speed (scroll right)
    const newDirection = Math.sign(distanceFromInitial);

    // Calculate speed based on distance and velocity
    const absDistance = Math.abs(distanceFromInitial);
    let targetSpeedLevel;

    // Check if we're moving in the opposite direction of the current scrolling
    const isOppositeDirection = currentDirection.current !== 0 && newDirection !== 0 && newDirection !== currentDirection.current;

    if (isOppositeDirection) {
      // If moving toward initial touch point, reduce speed significantly
      if (absDistance < dragThreshold * 0.5) {
        // Slow down as we approach initial touch point
        targetSpeedLevel = Math.min(1, Math.pow(absDistance / dragThreshold, 2));
      } else {
        // After passing initial point, allow faster speed scaling
        targetSpeedLevel = Math.min(maxSpeed * 0.5, Math.pow(absDistance / (dragThreshold * 0.5), 0.5)) * 1.5;
      }
    } else if (absDistance < dragThreshold * 0.5) {
      // Slow speed for small swipes
      targetSpeedLevel = Math.min(1.5, Math.pow(absDistance / dragThreshold, 1.8)) * 1.5;
    } else {
      // Faster speed for larger swipes, amplified by velocity
      targetSpeedLevel = Math.min(maxSpeed, Math.pow(absDistance / dragThreshold, 0.9) * (1 + absVelocity * 0.002)) * 1.5;
    }

    // Amplify speed based on velocity for fast swipes
    targetSpeedLevel *= Math.min(1.5, 1 + absVelocity * 0.002);

    // Apply direction to targetSpeed
    const targetSpeed = newDirection * targetSpeedLevel;

    // Adjust increment for smooth transitions
    const isReversing = currentDirection.current !== 0 && newDirection !== 0 && newDirection !== currentDirection.current;
    const isDecreasingSpeed = newDirection === currentDirection.current && Math.abs(targetSpeed) < Math.abs(scrollSpeed.current);
    const increment = isReversing ? fastDecrement * 2 : isDecreasingSpeed ? fastDecrement : speedIncrement;

    // Only update direction and speed if we've passed the initial touch point or are moving in the same direction
    if (newDirection !== 0 && (currentDirection.current === 0 || newDirection === currentDirection.current || absDistance > dragThreshold * 0.1)) {
      currentDirection.current = newDirection;
      scrollSpeed.current = lerp(scrollSpeed.current, targetSpeed, increment);
    } else if (isOppositeDirection && absDistance < dragThreshold * 0.1) {
      // When close to initial touch point during reverse swipe, slow down significantly
      scrollSpeed.current = lerp(scrollSpeed.current, 0, fastDecrement * 2);
    }

    // Update momentum for when finger is lifted
    if (timeDelta > 0) {
      const rawMomentum = movementX / timeDelta * 4;
      momentum.current = Math.sign(rawMomentum) * Math.max(Math.abs(rawMomentum), 0.1);
    }

    setTouch({
      movementX: movementX,
      active: true,
      distanceFromInitial: distanceFromInitial,
    });

    lastTouchX.current = touchX;
    lastTouchTime.current = now;
    touchStartX.current = touchX;
    lastMovementTime.current = now;
  };

  /**
   * Cleans up touch interaction
   * Resets touch-related values and user interaction state
   */
  const handleTouchEnd = () => {
    setTouch({ active: false });
    initialTouchX.current = 0;
    lastTouchX.current = 0;
    setTimeout(() => {
      isUserInteracting.current = false;
    }, 300);
  };

  /**
   * Initializes mouse interaction
   * Sets up initial mouse position and resets movement values
   */
  const handleMouseDown = (e) => {
    isUserInteracting.current = true;
    initialMouseX.current = e.clientX;
    dragDistance.current = 0;
    scrollSpeed.current = 0;
    currentDirection.current = 0;
    lastMovementTime.current = performance.now();
  };

  /**
   * Cleans up mouse interaction
   * Resets mouse-related values and user interaction state
   */
  const handleMouseUp = () => {
    initialMouseX.current = 0;
    setTimeout(() => {
      isUserInteracting.current = false;
    }, 300);
  };

  /**
   * Main animation frame update
   * Handles camera movement, bouncing, and momentum effects
   */
  useFrame(() => {
    const deltaPosition = camera.position.x - lastPosition.current;
    lastPosition.current = camera.position.x;

    if (isBouncingBack.current) {
      camera.position.x = lerp(camera.position.x, bounceTarget.current, 0.1);
      if (Math.abs(camera.position.x - bounceTarget.current) < 0.01) {
        isBouncingBack.current = false;
        momentum.current = 0;
        scrollSpeed.current = 0;
        setMovementDir(0);
      }
      bounceStrength.current *= 0.9;
      setMovementDir(bounceStrength.current);
    } else if (mouse && mouse.buttons > 0) {
      const speedFactor = Math.pow(Math.abs(scrollSpeed.current) / maxSpeed, 1.5) * 0.06;
      const newX = camera.position.x + Math.sign(scrollSpeed.current) * speedFactor;
      if (newX >= CAMERA_MAX - BOUNCE_MARGIN) {
        isBouncingBack.current = true;
        bounceTarget.current = CAMERA_MAX - BOUNCE_MARGIN * 2;
        bounceStrength.current = -Math.abs(scrollSpeed.current) * 0.001;
        momentum.current = 0;
        scrollSpeed.current = 0;
        initialMouseX.current = 0;
      } else if (newX <= CAMERA_MIN + BOUNCE_MARGIN) {
        isBouncingBack.current = true;
        bounceTarget.current = CAMERA_MIN + BOUNCE_MARGIN * 2;
        bounceStrength.current = Math.abs(scrollSpeed.current) * 0.001;
        momentum.current = 0;
        scrollSpeed.current = 0;
        initialMouseX.current = 0;
      } else {
        camera.position.x = newX;

        if (dragDistance.current !== 0) {
          if (Math.abs(dragDistance.current) > dragThreshold * 0.25 || Math.sign(dragDistance.current) === currentDirection.current) {
            const currentTilt = Math.sign(scrollSpeed.current) * Math.min(Math.abs(scrollSpeed.current), 1);
            setMovementDir((prev) => lerp(prev, currentTilt, 0.2));
          } else {
            setMovementDir((prev) => lerp(prev, currentDirection.current * 0.5, 0.2));
          }
        } else {
          setMovementDir(0);
        }
      }
    } else if (touch && touch.active) {
      // Continuous scrolling based on scrollSpeed
      const speedFactor = Math.pow(Math.abs(scrollSpeed.current) / maxSpeed, 1.5) * 0.06;
      const newX = camera.position.x + Math.sign(scrollSpeed.current) * speedFactor;
      if (newX >= CAMERA_MAX - BOUNCE_MARGIN) {
        isBouncingBack.current = true;
        bounceTarget.current = CAMERA_MAX - BOUNCE_MARGIN * 2;
        bounceStrength.current = -Math.abs(scrollSpeed.current) * 0.001;
        momentum.current = 0;
        scrollSpeed.current = 0;
        initialTouchX.current = 0;
      } else if (newX <= CAMERA_MIN + BOUNCE_MARGIN) {
        isBouncingBack.current = true;
        bounceTarget.current = CAMERA_MIN + BOUNCE_MARGIN * 2;
        bounceStrength.current = Math.abs(scrollSpeed.current) * 0.001;
        momentum.current = 0;
        scrollSpeed.current = 0;
        initialTouchX.current = 0;
      } else {
        camera.position.x = newX;

        if (touch.distanceFromInitial !== undefined && touch.distanceFromInitial !== 0) {
          if (Math.abs(touch.distanceFromInitial) > dragThreshold * 0.25 || Math.sign(touch.distanceFromInitial) === currentDirection.current) {
            const currentTilt = Math.sign(scrollSpeed.current) * Math.min(Math.abs(scrollSpeed.current), 1);
            setMovementDir((prev) => lerp(prev, currentTilt, 0.2));
          } else {
            setMovementDir((prev) => lerp(prev, currentDirection.current * 0.5, 0.2));
          }
        } else {
          setMovementDir(0);
        }
      }
    } else {
      if (!isBouncingBack.current && Math.abs(momentum.current) > 0.0001) {
        const momentumX = camera.position.x + momentum.current * 0.03;
        if (momentumX >= CAMERA_MAX - BOUNCE_MARGIN) {
          isBouncingBack.current = true;
          bounceTarget.current = CAMERA_MAX - BOUNCE_MARGIN * 2;
          bounceStrength.current = -Math.abs(momentum.current) * 0.001;
          momentum.current = 0;
          scrollSpeed.current = 0;
        } else if (momentumX <= CAMERA_MIN + BOUNCE_MARGIN) {
          isBouncingBack.current = true;
          bounceTarget.current = CAMERA_MIN + BOUNCE_MARGIN * 2;
          bounceStrength.current = Math.abs(momentum.current) * 0.001;
          momentum.current = 0;
          scrollSpeed.current = 0;
        } else {
          camera.position.x = momentumX;
          setMovementDir(Math.sign(momentum.current) * Math.min(Math.abs(momentum.current), 1));
          momentum.current *= MOMENTUM_DECAY;
        }
      } else if (isUserInteracting.current || isBouncingBack.current) {
        const significantDelta = Math.abs(deltaPosition) > 0.001;
        if (camera.position.x > CAMERA_MAX && significantDelta) {
          isBouncingBack.current = true;
          bounceTarget.current = CAMERA_MAX - BOUNCE_MARGIN * 2;
          bounceStrength.current = -Math.abs(deltaPosition) * 0.8;
          momentum.current = 0;
          scrollSpeed.current = 0;
        } else if (camera.position.x < CAMERA_MIN && significantDelta) {
          isBouncingBack.current = true;
          bounceTarget.current = CAMERA_MIN + BOUNCE_MARGIN * 2;
          bounceStrength.current = Math.abs(deltaPosition) * 0.8;
          momentum.current = 0;
          scrollSpeed.current = 0;
        } else if (!isBouncingBack.current) {
          setMovementDir((prev) => prev * TILT_RETURN_SPEED);
        }
      } else {
        if (camera.position.x > CAMERA_MAX) {
          camera.position.x = CAMERA_MAX - BOUNCE_MARGIN;
          momentum.current = 0;
          scrollSpeed.current = 0;
        } else if (camera.position.x < CAMERA_MIN) {
          camera.position.x = CAMERA_MIN + BOUNCE_MARGIN;
          momentum.current = 0;
          scrollSpeed.current = 0;
        }
        if (Math.abs(movementDir) > 0.001) {
          setMovementDir((prev) => prev * TILT_RETURN_SPEED);
        } else {
          setMovementDir(0);
        }
      }
    }

    const desiredLookAt = new THREE.Vector3(camera.position.x + movementDir * 0.8, 0, 0);
    target.current.lerp(desiredLookAt, 0.9);
    camera.lookAt(target.current);
  });

  /**
   * Sets up event listeners for mouse and touch interactions
   * Initializes camera position
   */
  useEffect(() => {
    const initialX = (CAMERA_MIN + CAMERA_MAX) / 2;
    camera.position.x = initialX;
    lastPosition.current = initialX;

    window.addEventListener("mousemove", handleMouseMove);
    window.addEventListener("mousedown", handleMouseDown);
    window.addEventListener("mouseup", handleMouseUp);
    window.addEventListener("touchstart", handleTouchStart, { passive: false });
    window.addEventListener("touchmove", handleTouchMove, { passive: false });
    window.addEventListener("touchend", handleTouchEnd);

    return () => {
      window.removeEventListener("mousemove", handleMouseMove);
      window.removeEventListener("mousedown", handleMouseDown);
      window.removeEventListener("mouseup", handleMouseUp);
      window.removeEventListener("touchstart", handleTouchStart);
      window.removeEventListener("touchmove", handleTouchMove);
      window.removeEventListener("touchend", handleTouchEnd);
    };
  }, [CAMERA_MIN, CAMERA_MAX]);

  /**
   * Sets up camera reference callback
   * Allows external control of camera navigation
   */
  useEffect(() => {
    if (cameraRef) {
      cameraRef(navigateToIndex);
    }
  }, [cameraRef, timelinesLength]);

  /**
   * Navigates camera to specific timeline index
   * @param {number} index - Target timeline index
   * @param {boolean} animate - Whether to animate the movement
   * @param {number} customDuration - Duration of animation in milliseconds
   */
  const navigateToIndex = (index, animate = true, customDuration = 1500) => {
    const effectiveLength = Math.max(timelinesLength, 1);
    let normalizedIndex = Math.max(-0.5, Math.min(index, effectiveLength - 0.5));

    const targetPosition = CAMERA_MIN + ((normalizedIndex + 0.5) / effectiveLength) * (CAMERA_MAX - CAMERA_MIN * 2);
    const startPosition = camera.position.x;

    if (!animate) {
      camera.position.x = targetPosition;
      lastPosition.current = camera.position.x;
      return;
    }

    let startTime = null;
    const duration = customDuration;

    const animateCamera = (timestamp) => {
      if (!startTime) startTime = timestamp;
      const elapsed = timestamp - startTime;
      const progress = Math.min(elapsed / duration, 1);

      const t = progress < 0.5
        ? 4 * progress * progress * progress
        : 1 - Math.pow(-2 * progress + 2, 3) / 2;

      camera.position.x = startPosition + t * (targetPosition - startPosition);
      lastPosition.current = camera.position.x;

      if (progress < 0.9) {
        const direction = Math.sign(targetPosition - startPosition);
        const tiltAmount = direction * Math.sin(progress * Math.PI) * 0.3;
        setMovementDir(tiltAmount);
      } else {
        setMovementDir(0);
      }

      if (progress < 1) {
        requestAnimationFrame(animateCamera);
      }
    };

    requestAnimationFrame(animateCamera);
  };

  return null;
};