/* eslint-disable react/prop-types */
import { useRef, useEffect, useState } from "react";
import * as THREE from "three";
import { useFrame } from "@react-three/fiber";

const getBackgroundColors = (year) => {
  if (!year) return { top: new THREE.Color(0.1, 0.1, 0.2), middle: new THREE.Color(0.15, 0.15, 0.25), bottom: new THREE.Color(0.2, 0.2, 0.3) };
  
  if (year >= 1960 && year <= 1969) {
    const color = new THREE.Color("#238BA6");
    return {
      top: new THREE.Color(color.r * 0.7, color.g * 0.7, color.b * 0.7),
      middle: color,
      bottom: new THREE.Color(color.r * 1.3, color.g * 1.3, color.b * 1.3)
    };
  } else if (year >= 1970 && year <= 1979) {
    const color = new THREE.Color("#45B0BA");
    return {
      top: new THREE.Color(color.r * 0.7, color.g * 0.7, color.b * 0.7),
      middle: color,
      bottom: new THREE.Color(color.r * 1.3, color.g * 1.3, color.b * 1.3)
    };
  } else if (year >= 1980 && year <= 1989) {
    const color = new THREE.Color("#8C70F8");
    return {
      top: new THREE.Color(color.r * 0.7, color.g * 0.7, color.b * 0.7),
      middle: color,
      bottom: new THREE.Color(color.r * 1.3, color.g * 1.3, color.b * 1.3)
    };
  } else if (year >= 1990 && year <= 1999) {
    const color = new THREE.Color("#8C70F8");
    return {
      top: new THREE.Color(color.r * 0.7, color.g * 0.7, color.b * 0.7),
      middle: color,
      bottom: new THREE.Color(color.r * 1.3, color.g * 1.3, color.b * 1.3)
    };
  } else if (year >= 2000 && year <= 2010) {
    const color = new THREE.Color("#6B4EF4");
    return {
      top: new THREE.Color(color.r * 0.7, color.g * 0.7, color.b * 0.7),
      middle: color,
      bottom: new THREE.Color(color.r * 1.3, color.g * 1.3, color.b * 1.3)
    };
  } else if (year >= 2011 && year <= 2020) {
    const color = new THREE.Color("#3433B4");
    return {
      top: new THREE.Color(color.r * 0.7, color.g * 0.7, color.b * 0.7),
      middle: color,
      bottom: new THREE.Color(color.r * 1.3, color.g * 1.3, color.b * 1.3)
    };
  }
  
  return { top: new THREE.Color(0.1, 0.1, 0.2), middle: new THREE.Color(0.15, 0.15, 0.25), bottom: new THREE.Color(0.2, 0.2, 0.3) };
};

export const BackGround = ({ timelinesLength, currentYear }) => {
  const bgRef = useRef(null);
  const materialRef = useRef(null);
  const [colors, setColors] = useState({
    top: new THREE.Color(0.1, 0.1, 0.2),
    middle: new THREE.Color(0.15, 0.15, 0.25),
    bottom: new THREE.Color(0.2, 0.2, 0.3)
  });
  const targetColors = useRef(colors);

  useEffect(() => {
    if (bgRef.current) {
      bgRef.current.scale.x = Math.max(timelinesLength * 10, 20);
      bgRef.current.scale.y = window.innerHeight;
      bgRef.current.position.y = -2;
    }
  }, [timelinesLength]);

  useEffect(() => {
    targetColors.current = getBackgroundColors(currentYear);
  }, [currentYear]);

  useFrame(() => {
    if (materialRef.current) {
      setColors({
        top: colors.top.lerp(targetColors.current.top, 0.05),
        middle: colors.middle.lerp(targetColors.current.middle, 0.05),
        bottom: colors.bottom.lerp(targetColors.current.bottom, 0.05)
      });

      materialRef.current.uniforms.colorTop.value = colors.top;
      materialRef.current.uniforms.colorMiddle.value = colors.middle;
      materialRef.current.uniforms.colorBottom.value = colors.bottom;
    }
  });

  return (
    <mesh ref={bgRef} rotation={[Math.PI, 0, 0]} position={[0, 0, 0]}>
      <planeGeometry args={[1, 1]} />
      <shaderMaterial
        ref={materialRef}
        vertexShader={`
          varying vec2 vUv;
          void main() {
              vUv = uv;
              gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );
          }
        `}
        fragmentShader={`
          varying vec2 vUv;
          uniform vec3 colorTop;
          uniform vec3 colorMiddle;
          uniform vec3 colorBottom;

          void main() {
              vec3 gradientColor = mix(
                  mix(colorBottom, colorMiddle, smoothstep(0.0, 0.5, vUv.y)),
                  colorTop,
                  smoothstep(0.5, 1.0, vUv.y)
              );
              gl_FragColor = vec4(gradientColor, 1.0);
          }
        `}
        uniforms={{
          colorTop: { value: colors.top },
          colorMiddle: { value: colors.middle },
          colorBottom: { value: colors.bottom }
        }}
      />
    </mesh>
  );
};