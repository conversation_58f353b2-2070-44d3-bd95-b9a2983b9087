import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { signOut } from '../redux/user/userSlice';
import { useState } from 'react';
import {
  // HomeIcon,
  PlusCircleIcon,
  ListBulletIcon,
  ChartBarIcon,
  ArrowLeftOnRectangleIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  Cog6ToothIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import { Dialog } from '@headlessui/react';
import TrackingToggle from './TrackingToggle';

const Sidebar = () => {
  const { currentUser } = useSelector((state) => state.user);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const handleSignOut = async () => {
    try {
      // const res = await fetch('/v2/auth/signout', {
      // const res = await fetch(`${import.meta.env.VITE_API_IP}/v2/auth/logout`, {
      const res = await fetch(`${import.meta.env.VITE_API_IP}/auth/logout`, {
        method: 'POST',
      });
      const data = await res.json();
      if (data.success === false) {
        console.log(data.message);
        return;
      }
      dispatch(signOut());
      navigate('/adminpanel/sign-in');
    } catch (error) {
      console.log(error);
    }
  };

  const navItems = [
    {
      name: 'Event List',
      path: '/adminpanel/event',
      icon: ListBulletIcon,
    },
    {
      name: 'Create Event',
      path: '/adminpanel/create-event',
      icon: PlusCircleIcon,
    },
    // {
    //   name: 'Event',
    //   path: '/event-sequence',
    //   icon: HomeIcon,
    // },
    {
      name: 'Analytics',
      path: '/adminpanel/analytics',
      icon: ChartBarIcon,
    },
  ];

  return (
    <div
      className={`fixed left-0 top-0 h-full bg-white shadow-lg transition-all duration-300 ${
        isCollapsed ? 'w-20' : 'w-64'
      }`}
    >
      <div className="flex flex-col h-full">
        {/* Toggle Button */}
        <button
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="p-4 text-gray-600 hover:text-gray-900 focus:outline-none"
          aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          {isCollapsed ? (
            <ChevronRightIcon className="h-6 w-6" />
          ) : (
            <ChevronLeftIcon className="h-6 w-6" />
          )}
        </button>

        {/* Navigation Items */}
        <nav className="flex-1 px-4 py-2">
          <ul className="space-y-2">
            {navItems.map((item) => {
              const Icon = item.icon;
              const isActive = item.path === '/event' 
                ? (location.pathname === '/event' || location.pathname === '/') 
                : location.pathname === item.path;
              return (
                <li key={item.path}>
                  <Link
                    to={item.path}
                    className={`flex items-center p-3 rounded-lg transition-colors duration-200 ${
                      isActive
                        ? 'bg-blue-50 text-blue-600'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <Icon className="h-6 w-6" />
                    {!isCollapsed && (
                      <span className="ml-3 font-medium">{item.name}</span>
                    )}
                  </Link>
                </li>
              );
            })}
          </ul>
        </nav>

        {/* Settings and Sign Out */}
        <div className="p-4 border-t border-gray-200 space-y-2">
          <button
            onClick={() => setIsSettingsOpen(true)}
            className="flex items-center w-full p-3 text-gray-600 rounded-lg hover:bg-gray-50 hover:text-gray-900 transition-colors duration-200"
            aria-label="Open settings"
          >
            <Cog6ToothIcon className="h-6 w-6" />
            {!isCollapsed && <span className="ml-3 font-medium">Settings</span>}
          </button>
          {currentUser && (
            <button
              onClick={handleSignOut}
              className="flex items-center w-full p-3 text-red-600 rounded-lg hover:bg-red-50 transition-colors duration-200"
            >
              <ArrowLeftOnRectangleIcon className="h-6 w-6" />
              {!isCollapsed && (
                <span className="ml-3 font-medium">Sign Out</span>
              )}
            </button>
          )}
        </div>
      </div>

      {/* Settings Dialog */}
      <Dialog
        open={isSettingsOpen}
        onClose={() => setIsSettingsOpen(false)}
        className="relative z-50"
      >
        <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

        <div className="fixed inset-0 flex items-center justify-center p-4">
          <Dialog.Panel className="w-full max-w-md bg-white rounded-lg shadow-xl">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <Dialog.Title className="text-xl font-semibold text-gray-900">
                  Settings
                </Dialog.Title>
                <button
                  onClick={() => setIsSettingsOpen(false)}
                  className="text-gray-400 hover:text-gray-500"
                  aria-label="Close settings"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              <div className="space-y-6">
                <div className="border-b border-gray-200 pb-4">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Analytics Settings
                  </h3>
                  <TrackingToggle />
                </div>
              </div>
            </div>
          </Dialog.Panel>
        </div>
      </Dialog>
    </div>
  );
};

export default Sidebar; 