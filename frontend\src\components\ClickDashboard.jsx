import { useEffect, useState } from 'react';
import { FaChartBar, FaSync, FaToggleOn, FaToggleOff } from 'react-icons/fa';
import { get, put } from '../utils/api';
import ClickDashboardSkeleton from './ClickDashboardSkeleton';

const ClickDashboard = () => {
    const [counts, setCounts] = useState({
        our_journey: 0,
        highlights: 0,
        sustainability: 0,
        dark_days: 0
    });
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [refreshing, setRefreshing] = useState(false);
    const [trackingEnabled, setTrackingEnabled] = useState(true);
    const [updatingTracking, setUpdatingTracking] = useState(false);

    const fetchClickCounts = async () => {
        try {
            setError(null);
            setRefreshing(true);
            
            // First try the test endpoint to check connectivity
            try {
                const testResponse = await get(`${import.meta.env.VITE_API_IP}/keymilestone/test`);
                
                if (!testResponse.ok) {
                    throw new Error('Test endpoint failed');
                }
                
                const testData = await testResponse.json();
                console.log('API test response:', testData);
            } catch (testError) {
                console.warn('Test endpoint failed:', testError.message);
            }
            
            // Try the new endpoint first
            try {
                // console.log('Trying new API endpoint...');
                const response = await get(`${import.meta.env.VITE_API_IP}/keymilestone/all`);
                
                if (!response.ok) {
                    throw new Error('New API endpoint failed');
                }

                const data = await response.json();
                // console.log('API response data:', data);

                if (data && data.status === "success") {
                    // Transform the data format to match our state structure
                    const transformedCounts = {
                        our_journey: 0,
                        highlights: 0,
                        sustainability: 0,
                        dark_days: 0
                    };

                    // Map the numeric keys back to their string counterparts
                    const keyMap = {
                        1: 'our_journey',
                        2: 'highlights',
                        3: 'sustainability',
                        4: 'dark_days'
                    };

                    data.data.forEach(item => {
                        const stringKey = keyMap[item.key];
                        if (stringKey) {
                            transformedCounts[stringKey] = item.value;
                        }
                    });

                    setCounts(transformedCounts);
                    setTrackingEnabled(data.tracking_enabled);
                    return;
                }
            } catch (newApiError) {
                console.warn('New API endpoint failed, falling back to original endpoint:', newApiError.message);
            }
            
            // Fallback to original endpoint
            const response = await get(`${import.meta.env.VITE_API_IP}/v2/click/counts/public`);

            if (!response.ok) {
                throw new Error('Failed to fetch click counts');
            }

            const data = await response.json();
            // console.log('Fallback API response data:', data);
            
            if (data && data.success) {
                setCounts(data.counts);
            } else {
                throw new Error(data?.message || 'Error fetching click counts');
            }
        } catch (err) {
            console.error('Error fetching click data:', err);
            setError(err.message);
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };

    const fetchTrackingStatus = async () => {
        try {
            const response = await get(`${import.meta.env.VITE_API_IP}/v2/tracking/status`);

            if (!response.ok) {
                throw new Error('Failed to fetch tracking status');
            }

            const data = await response.json();
            if (data.success) {
                setTrackingEnabled(data.isEnabled);
            } else {
                throw new Error(data?.message || 'Error fetching tracking status');
            }
        } catch (err) {
            console.error("Error fetching tracking status:", err);
        }
    };

    const toggleTracking = async () => {
        try {
            setUpdatingTracking(true);
            const response = await put(`${import.meta.env.VITE_API_IP}/v2/tracking/status`, { isEnabled: !trackingEnabled });

            if (!response.ok) {
                throw new Error('Failed to update tracking status');
            }

            const data = await response.json();
            if (data.success) {
                setTrackingEnabled(data.isEnabled);
            } else {
                throw new Error(data?.message || 'Error updating tracking status');
            }
        } catch (err) {
            console.error("Error updating tracking status:", err);
        } finally {
            setUpdatingTracking(false);
        }
    };

    useEffect(() => {
        fetchClickCounts();
        fetchTrackingStatus();
    }, []);

    const handleRefresh = () => {
        fetchClickCounts();
        fetchTrackingStatus();
    };

    const getKeyStyles = (key) => {
        const styleMap = {
            'our_journey': 'bg-purple-100 text-purple-600',
            'highlights': 'bg-orange-100 text-orange-600',
            'sustainability': 'bg-green-100 text-green-600',
            'dark_days': 'bg-red-100 text-red-600'
        };
        return styleMap[key] || 'bg-gray-100 text-gray-600';
    };

    const formatKey = (key) => {
        const keyMap = {
            'our_journey': 'Our Journey',
            'highlights': 'Highlights',
            'sustainability': 'Sustainability',
            'dark_days': 'Dark Days'
        };
        return keyMap[key] || key;
    };

    if (loading) return <ClickDashboardSkeleton />;

    if (error) return (
        <div className="text-center py-4">
            <p className="text-red-600 font-medium">{error}</p>
        </div>
    );

    return (
        <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-2">
                    <FaChartBar className="text-blue-500 text-2xl" />
                    <h2 className="text-2xl font-semibold text-gray-800">Hotspot Click Analytics</h2>
                </div>
                <div className="flex gap-2">
                    {/* Only show tracking toggle to admins */}
                    <button 
                        onClick={toggleTracking}
                        disabled={updatingTracking}
                        className="flex items-center gap-1 px-3 py-1 bg-gray-100 text-gray-800 rounded hover:bg-gray-200 disabled:opacity-50"
                    >
                        {trackingEnabled ? <FaToggleOn className="text-green-500 text-xl" /> : <FaToggleOff className="text-red-500 text-xl" />}
                        Tracking {trackingEnabled ? 'ON' : 'OFF'}
                    </button>
                    <button 
                        onClick={handleRefresh}
                        disabled={refreshing}
                        className="flex items-center gap-1 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
                    >
                        <FaSync className={refreshing ? "animate-spin" : ""} />
                        Refresh
                    </button>
                </div>
            </div>
            {!trackingEnabled && (
                <div className="mb-4 p-3 bg-yellow-100 text-yellow-800 rounded-md">
                    ⚠️ Click tracking is currently disabled. Enable tracking to collect click data.
                </div>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {Object.entries(counts).map(([key, count]) => (
                    <div key={key} className="bg-white rounded-lg border border-gray-200 p-4">
                        <div className={`inline-block px-3 py-1 rounded-full text-sm font-medium mb-2 ${getKeyStyles(key)}`}>
                            {formatKey(key)}
                        </div>
                        <div className="text-3xl font-bold text-gray-800">{count}</div>
                        <div className="text-sm text-gray-500">Total Clicks</div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default ClickDashboard; 