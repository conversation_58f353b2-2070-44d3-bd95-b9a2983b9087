/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import { useRef, useEffect, useMemo, useState } from "react";
import * as THREE from "three";
import { useFrame } from "@react-three/fiber";
import { SimplexNoise } from "three/addons/math/SimplexNoise.js";
import { Line2 } from "three/examples/jsm/lines/Line2.js";
import { LineGeometry } from "three/examples/jsm/lines/LineGeometry.js";
import { LineMaterial } from "three/examples/jsm/lines/LineMaterial.js";
import { SpriteItem } from "./SpriteItem";

const vertShader = `
attribute float scale;
varying float vAlpha;

void main() {
    vec4 mvPosition = modelViewMatrix * vec4( position, 1 );
    gl_PointSize = 1.8;
    gl_Position = projectionMatrix * mvPosition;

    vAlpha = scale;
}
`;

const fragShader = `
uniform vec3 color;
varying float vAlpha;

void main() {
    if ( length( gl_PointCoord - vec2( 0.5, 0.5 ) ) > 0.9 ) discard;
    gl_FragColor = vec4( color, vAlpha * 0.8 ); // apply fade
}
`;

export const Particles = ({ lineColor = "#ffffff", lineActive = false, timelines, visibleTimelines, openCards, setOpenCards, setCameraRef, onYearChange, onWaveAnimationComplete, startWaveAnimation, activeCategories = [] }) => {
  const SEPARATION = 0.005;
  const AMOUNTX = Math.max(timelines.length * 47, 47); // Ensure at least one row for initial render
  const AMOUNTY = 90;

  const particlesRef = useRef();
  const spriteRef = useRef();
  const lineRef = useRef();
  const lineMatRef = useRef();
  const categoryLinesRef = useRef([]); // Array to hold multiple lines for categories
  const lastYearRef = useRef(null);

  const simplex = useMemo(() => new SimplexNoise(), []);
  const waveShift = useRef(0);
  const [waveOffset, setWaveOffset] = useState(-0.5); // Start wave below normal position
  const [isWaveAnimating, setIsWaveAnimating] = useState(false);
  const [hasAnimated, setHasAnimated] = useState(false); // Track if animation has run

  // Reset animation state when startWaveAnimation changes to false
  useEffect(() => {
    if (!startWaveAnimation) {
      setWaveOffset(-0.5);
      setHasAnimated(false);
    }
  }, [startWaveAnimation]);

  // Trigger wave animation based on startWaveAnimation prop
  useEffect(() => {
    // console.log("Wave animation effect triggered", {
    //   startWaveAnimation,
    //   waveOffset,
    //   hasAnimated,
    //   timelinesLength: timelines.length
    // });

    if (startWaveAnimation && waveOffset < 0 && !hasAnimated) {
      // console.log("Starting wave animation");
      setIsWaveAnimating(true);
      setHasAnimated(true); // Mark animation as run
      const duration = 2000; // 2 seconds for wave animation
      const startTime = performance.now();

      const animate = (time) => {
        const elapsed = time - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const ease = 1 - Math.pow(1 - progress, 3); // Ease-out cubic
        setWaveOffset(-0.5 + ease * 0.5);

        if (progress < 1) {
          requestAnimationFrame(animate);
        } else {
          // console.log("Wave animation complete, calling onWaveAnimationComplete");
          setIsWaveAnimating(false);
          if (onWaveAnimationComplete) {
            onWaveAnimationComplete(timelines.length);
          }
        }
      };

      requestAnimationFrame(animate);
    }
  }, [startWaveAnimation, onWaveAnimationComplete, timelines.length, waveOffset, hasAnimated]);

  // Synchronize openCards with visibleTimelines
  useEffect(() => {
    const visibleIndices = visibleTimelines.map((item) => timelines.findIndex((t) => t._id === item._id));
    setOpenCards((prev) => {
      const filteredCards = prev.filter((card) => visibleIndices.includes(card.index));
      return filteredCards;
    });
  }, [visibleTimelines, timelines, setOpenCards]);

  useEffect(() => {
    const handleResize = () => {
      if (lineMatRef.current) {
        lineMatRef.current.resolution.set(window.innerWidth, window.innerHeight);
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    if (!particlesRef.current) return;

    const numParticles = AMOUNTX * AMOUNTY;
    const positions = new Float32Array(numParticles * 3);
    const scales = new Float32Array(numParticles);

    let i = 0,
      j = 0;
    for (let ix = 0; ix < AMOUNTX; ix++) {
      for (let iy = 0; iy < AMOUNTY; iy++) {
        positions[i] = ix * SEPARATION;
        positions[i + 1] = 0;
        positions[i + 2] = iy * SEPARATION - (AMOUNTY * SEPARATION) / 2;

        const fade = Math.sin((Math.PI * ix) / AMOUNTX);
        scales[j] = fade;

        i += 3;
        j++;
      }
    }

    const geometry = new THREE.BufferGeometry();
    geometry.setAttribute("position", new THREE.BufferAttribute(positions, 3));
    geometry.setAttribute("scale", new THREE.BufferAttribute(scales, 1));

    const material = new THREE.ShaderMaterial({
      uniforms: {
        color: { value: new THREE.Color(0xffffff) },
      },
      vertexShader: vertShader,
      fragmentShader: fragShader,
      transparent: true,
    });

    particlesRef.current.geometry = geometry;
    particlesRef.current.material = material;
  }, [AMOUNTX]);

  useFrame(({ clock, camera }) => {
    if (!particlesRef.current || !particlesRef.current.geometry.attributes.position) return;

    const positions = particlesRef.current.geometry.attributes.position.array;
    const time = clock.getElapsedTime();

    waveShift.current += 0.001;

    let i = 0,
      j = 0;
    for (let ix = 0; ix < AMOUNTX; ix++) {
      for (let iy = 0; iy < AMOUNTY; iy++) {
        const x = ix * SEPARATION;
        const z = iy * SEPARATION;

        const y = (0.10 * simplex.noise3d(x / 0.62 + waveShift.current, z / 0.33, time / 30)) + waveOffset;

        positions[i + 1] = y;
        i += 3;
        j++;
      }
    }

    particlesRef.current.geometry.attributes.position.needsUpdate = true;
    particlesRef.current.geometry.attributes.scale.needsUpdate = true;

    if (spriteRef.current) {
      spriteRef.current.children.forEach((el, idx) => {
        if (el && el.isObject3D) {
          const spriteId = el.userData.id;
          const originalIndex = timelines.findIndex((item) => item._id === spriteId);
          if (originalIndex !== -1) {
            const reversedIndex = timelines.length - 1 - originalIndex;
            const positionIndex = reversedIndex * 47 * AMOUNTY * 3;
            if (positionIndex >= 0 && positionIndex < positions.length) {
              el.position.set(
                positions[positionIndex],
                positions[positionIndex + 1],
                positions[positionIndex + 2]
              );
            }
          }
        }
      });
    }

    if (lineRef.current && lineRef.current.geometry) {
      const linePoints = [];
      let index = 0;

      for (let i = 0; i < AMOUNTX; i++) {
        linePoints.push(positions[index], positions[index + 1], positions[index + 2]);
        index += AMOUNTY * 3;
      }

      lineRef.current.geometry.setPositions(linePoints);

      if (lineMatRef.current) {
        const shouldHideLine = !lineActive || lineColor === "transparent" || lineColor === "none" || lineColor === "rgba(0,0,0,0)";
        if (!shouldHideLine) {
          lineMatRef.current.color.set(lineColor);
          lineMatRef.current.opacity = 1;
        } else {
          lineMatRef.current.color.set("#000000");
          lineMatRef.current.opacity = 0;
        }
      }
    }

    // Handle multiple category lines
    if (activeCategories && activeCategories.length > 0) {
      activeCategories.forEach((category, categoryIndex) => {
        if (categoryLinesRef.current[categoryIndex] && categoryLinesRef.current[categoryIndex].geometry) {
          const linePoints = [];
          let index = 0;
          
          // Create unique wave characteristics for each category line
          const waveFrequency = 0.62 + (categoryIndex * 0.15); // Different frequency for each line
          const waveSpeed = 30 + (categoryIndex * 5); // Different speed for each line
          const waveAmplitude = 0.10 + (categoryIndex * 0.03); // Slightly different amplitude
          const phaseOffset = categoryIndex * Math.PI * 0.4; // Phase shift for natural variation

          for (let i = 0; i < AMOUNTX; i++) {
            const x = i * SEPARATION * AMOUNTX / AMOUNTX;
            const z = 0; // Using center line as base
            
            // Generate unique wave pattern for this category
            const uniqueWaveY = waveAmplitude * simplex.noise3d(
              x / waveFrequency + waveShift.current + phaseOffset, 
              z / 0.33 + (categoryIndex * 0.2), 
              time / waveSpeed
            ) + waveOffset;
            
            linePoints.push(
              positions[index], 
              uniqueWaveY, // Each line has its own wave pattern
              positions[index + 2]
            );
            index += AMOUNTY * 3;
          }

          categoryLinesRef.current[categoryIndex].geometry.setPositions(linePoints);

          if (categoryLinesRef.current[categoryIndex].material) {
            categoryLinesRef.current[categoryIndex].material.color.set(category.color);
            categoryLinesRef.current[categoryIndex].material.opacity = 1;
          }
        }
      });

      // Hide unused category lines
      for (let i = activeCategories.length; i < categoryLinesRef.current.length; i++) {
        if (categoryLinesRef.current[i] && categoryLinesRef.current[i].material) {
          categoryLinesRef.current[i].material.opacity = 0;
        }
      }

      // Hide main line when showing category lines
      if (lineMatRef.current && activeCategories.length > 1) {
        lineMatRef.current.opacity = 0;
      }
    } else {
      // Hide all category lines when no categories are active
      categoryLinesRef.current.forEach(line => {
        if (line && line.material) {
          line.material.opacity = 0;
        }
      });
    }

    if (timelines.length > 0 && onYearChange && spriteRef.current) {
      const cameraX = camera.position.x;
      let closestIndex = 0;
      let minDistance = Infinity;

      spriteRef.current.children.forEach((child, idx) => {
        if (child && child.position) {
          const distance = Math.abs(child.position.x - cameraX);
          if (distance < minDistance) {
            minDistance = distance;
            closestIndex = timelines.findIndex((item) => item._id === child.userData.id);
          }
        }
      });

      if (closestIndex >= 0 && closestIndex < timelines.length) {
        const year = timelines[closestIndex]?.year;
        if (year && year !== lastYearRef.current) {
          lastYearRef.current = year;
          onYearChange(year);
        }
      }
    }
  });

  useEffect(() => {
    if (setCameraRef && spriteRef.current) {
      const navigateToNode = (index) => {
        if (index >= 0 && index < timelines.length) {
          setCameraRef((navigate) => {
            if (navigate) navigate(index);
            return navigate;
          });
        }
      };

      setCameraRef(navigateToNode);
    }
  }, [setCameraRef, timelines.length]);

  const Sprites = useMemo(
    () =>
      timelines.map((item, i) => {
        const isVisible = visibleTimelines.some((visibleItem) => visibleItem._id === item._id);
        if (!isVisible) return null;

        return (
          <SpriteItem
            key={i}
            item={item}
            index={i}
            openCards={openCards}
            setOpenCards={setOpenCards}
            userData={{ id: item._id }}
          />
        );
      }),
    [timelines, visibleTimelines, openCards, setOpenCards]
  );

  // Create category lines using useMemo
  const categoryLines = useMemo(() => {
    const lines = [];
    for (let index = 0; index < 5; index++) {
      const geometry = new LineGeometry();
      const initialPositions = new Float32Array(AMOUNTX * 3);
      geometry.setPositions(initialPositions);
      const material = new LineMaterial({
        color: "#ffffff",
        linewidth: 4,
        vertexColors: false,
        dashed: false,
        alphaToCoverage: false,
        transparent: true,
        opacity: 0,
        resolution: new THREE.Vector2(window.innerWidth, window.innerHeight),
      });
      
      const line = new Line2(geometry, material);
      
      // Initialize category lines array if needed
      if (!categoryLinesRef.current) {
        categoryLinesRef.current = [];
      }
      categoryLinesRef.current[index] = line;
      
      lines.push(
        <primitive
          key={`category-line-${index}`}
          object={line}
        />
      );
    }
    return lines;
  }, [AMOUNTX]);

  return (
    <group>
      <group ref={spriteRef}>{Sprites}</group>
      <points ref={particlesRef} />
      <primitive
        object={useMemo(() => {
          const geometry = new LineGeometry();
          const initialPositions = new Float32Array(AMOUNTX * 3);
          geometry.setPositions(initialPositions);
          const material = new LineMaterial({
            color: lineColor,
            linewidth: 4,
            vertexColors: false,
            dashed: false,
            alphaToCoverage: false,
            transparent: true,
            opacity: lineActive ? 1 : 0,
            resolution: new THREE.Vector2(window.innerWidth, window.innerHeight),
          });
          lineMatRef.current = material;
          return new Line2(geometry, material);
        }, [lineColor])}
        ref={lineRef}
      />
      {/* Render multiple lines for categories */}
      {categoryLines}
    </group>
  );
};