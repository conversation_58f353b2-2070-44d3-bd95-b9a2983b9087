/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
import { useEffect, useState, useRef } from "react";

// PopupCards Component
const PopupCards = ({ timelines, openCards, setOpenCards }) => {
  // State for managing the current media index in the carousel for each Card
  const [mediaIndices, setMediaIndices] = useState({});
  const cardRefs = useRef({});

  // Helper function to close a Card
  const closeCard = (index) => {
    // Find the card with this main index
    const cardToClose = openCards.find(card => card.index === index);
    
    if (cardToClose) {
      // Create a new array without this card and maintain positions of other cards
      const newCards = openCards.filter(card => card.index !== index);
      
      // Don't reposition remaining cards - keep them where they are
      setOpenCards(newCards);
    }
    
    // Reset media index for the closed Card
    setMediaIndices((prev) => {
      const newIndices = { ...prev };
      delete newIndices[index];
      return newIndices;
    });
  };

  // Helper function to get styles based on category key
  const getKeyStyles = (key) => {
    const styleMap = {
      our_journey: { color: "#6D0CE7" },
      highlights: { color: "#F49C53" },
      sustainability: { color: "#32D5B3" },
      dark_days: { color: "#FD1030" },
    };
    return styleMap[key] || { color: "#4B5563" }; // Default to gray-600 equivalent
  };

  // Helper function to get background styles for the dot based on category key
  const getDotStyles = (key) => {
    const styleMap = {
      our_journey: { backgroundColor: "#6D0CE7" },
      highlights: { backgroundColor: "#F49C53" },
      sustainability: { backgroundColor: "#32D5B3" },
      dark_days: { backgroundColor: "#FD1030" },
    };
    return styleMap[key] || { backgroundColor: "#4B5563" }; // Default to gray-600 equivalent
  };

  // Helper function to get position classes
  const getPositionClasses = (position) => {
    switch (position) {
      case "left":
        return "left-[5%]";
      case "right":
        return "right-[5%]";
      case "center":
      default:
        return "left-1/2 -translate-x-1/2";
    }
  };

  // Ensure Cards are visible when opened
  useEffect(() => {
    // First, update the card refs and make sure all cards have valid, non-overlapping positions
    let updatedOpenCards = [...openCards];
    
    // Check for duplicate positions and fix them
    const positions = updatedOpenCards.map(card => card.position);
    const uniquePositions = [...new Set(positions)];
    
    // If we have duplicate positions, reassign them
    if (uniquePositions.length < positions.length) {
      const availablePositions = ["left", "center", "right"].filter(
        pos => !uniquePositions.includes(pos) || 
        positions.filter(p => p === pos).length > 1
      );
      
      let positionIndex = 0;
      updatedOpenCards = updatedOpenCards.map((card, i) => {
        // If this position appears multiple times, reassign all but the first one
        if (i > 0 && positions.indexOf(card.position) !== positions.lastIndexOf(card.position) &&
            positions.indexOf(card.position) < i) {
          const newPosition = availablePositions[positionIndex % availablePositions.length];
          positionIndex++;
          return { ...card, position: newPosition };
        }
        return card;
      });
      
      // Update the state if we fixed any duplicates
      if (JSON.stringify(updatedOpenCards) !== JSON.stringify(openCards)) {
        setOpenCards(updatedOpenCards);
        return;
      }
    }
    
    // Now show all cards with proper animations
    openCards.forEach((card) => {
      const cardEl = document.getElementById(`card-${card.index}`);
      if (cardEl) {
        // Store reference to this card
        cardRefs.current[card.index] = cardEl;
        
        // Make card visible with a slight delay for animation
        setTimeout(() => {
          cardEl.style.display = "flex";
          cardEl.style.opacity = "1";
        }, 1500);
      }
    });
    
    // Clean up any removed cards
    Object.keys(cardRefs.current).forEach(idx => {
      if (!openCards.some(card => card.index.toString() === idx)) {
        delete cardRefs.current[idx];
      }
    });
  }, [openCards]);

  // Swipe handling for the carousel
  const handleSwipe = (cardIndex, direction) => {
    const item = timelines[cardIndex];
    const mediaItems = [];
    if (item.thumbnail) mediaItems.push({ type: "image", path: item.thumbnail });
    if (item.files && item.files.length > 0) {
      item.files.forEach((file) => mediaItems.push(file));
    }

    setMediaIndices((prev) => {
      const currentIndex = prev[cardIndex] || 0;
      let newIndex;
      if (direction === "left") {
        newIndex = Math.min(currentIndex + 1, mediaItems.length - 1);
      } else {
        newIndex = Math.max(currentIndex - 1, 0);
      }

      // Autoplay video if the new index is a video
      if (mediaItems[newIndex]?.type === "video") {
        const videoElement = document.getElementById(`video-${cardIndex}`);
        if (videoElement) {
          videoElement.play();
        }
      }

      return { ...prev, [cardIndex]: newIndex };
    });
  };

  // Sort cards by position to ensure consistent rendering order
  const sortedCards = [...openCards].sort((a, b) => {
    const positionOrder = { left: 0, center: 1, right: 2 };
    return positionOrder[a.position] - positionOrder[b.position];
  });

  return (
    <>
      {sortedCards.map((card) => {
        const item = timelines[card.index];
        if (!item) return null;

        // Prepare media items (thumbnail + files)
        const mediaItems = [];
        if (item.thumbnail) mediaItems.push({ type: "image", path: item.thumbnail });
        if (item.files && item.files.length > 0) {
          item.files.forEach((file) => mediaItems.push(file));
        }

        const currentMediaIndex = mediaIndices[card.index] || 0;

        return (
          <div
            key={`card-${card.index}`}
            id={`card-${card.index}`}
            className={`transition-all duration-300 opacity-0 hidden flex-col w-[calc(100vw/4)] max-w-md rounded-xl bg-white h-[45vh] fixed z-[100] top-1/2 -translate-y-1/2 shadow-lg backdrop-blur-sm bg-opacity-95 overflow-hidden ${getPositionClasses(card.position)}`}
          >
            {/* Carousel Section */}
            <div className="relative w-full h-64 flex-shrink-0 overflow-hidden rounded-t-xl">
              {/* Media Display */}
              <div className="w-full h-full flex transition-transform duration-300" style={{ transform: `translateX(-${currentMediaIndex * 100}%)` }}>
                {mediaItems.map((media, index) => (
                  <div key={index} className="w-full h-full flex-shrink-0">
                    {media.type === "image" ? (
                      <img
                        src={`${import.meta.env.VITE_API_IP}/uploads/${media.path}`}
                        alt={`${item.title || "Timeline"} - Media ${index + 1}`}
                        className="w-full h-full object-cover rounded-t-xl"
                      />
                    ) : (
                      <video
                        id={`video-${card.index}`}
                        src={`${import.meta.env.VITE_API_IP}/uploads/${media.path}`}
                        muted
                        autoPlay
                        className="w-full h-full object-cover rounded-t-xl"
                      />
                    )}
                  </div>
                ))}
              </div>

              {/* Navigation Arrows */}
              {mediaItems.length > 1 && (
                <>
                  <button
                    onClick={() => handleSwipe(card.index, "right")}
                    disabled={currentMediaIndex === 0}
                    className={`absolute left-2 top-1/2 -translate-y-1/2 w-8 h-8 rounded-full flex items-center justify-center z-[101] ${
                      currentMediaIndex === 0 ? "opacity-50 cursor-not-allowed" : "hover:bg-white"
                    }`}
                  >
                    <img
                      src="/milestone-v2/icons/left-btn.png"
                      alt="Previous"
                      className="w-7 h-7"
                    />
                  </button>
                  <button
                    onClick={() => handleSwipe(card.index, "left")}
                    disabled={currentMediaIndex === mediaItems.length - 1}
                    className={`absolute right-2 top-1/2 -translate-y-1/2 w-8 h-8 rounded-full flex items-center justify-center z-[101] ${
                      currentMediaIndex === mediaItems.length - 1 ? "opacity-50 cursor-not-allowed" : "hover:bg-white"
                    }`}
                  >
                    <img
                      src="/milestone-v2/icons/right-btn.png"
                      alt="Next"
                      className="w-7 h-7"
                    />
                  </button>
                </>
              )}
            </div>

            {/* Close Button */}
            <button
              onClick={() => closeCard(card.index)}
              className="absolute top-4 right-2 w-8 h-8 rounded-full flex items-center justify-center z-[101] transition-colors"
            >
              <img
                src="/milestone-v2/icons/close-btn.png"
                alt="Close"
                className="w-7 h-7"
              />
            </button>

            {/* Content Section */}
            <div className="flex-1 overflow-y-auto overflow-x-hidden px-6 py-4">
              <div className="flex items-start gap-2">
                <h2
                  className={`text-xl font-semibold break-words flex-shrink-0`}
                  style={{
                    fontFamily: "Prometo Bold",
                    fontSize: "20px",
                    letterSpacing: "0.05em",
                    lineHeight: "1.0",
                    verticalAlign: "top",
                    ...getKeyStyles(item.key)
                  }}
                >
                  {item.year || "N/A"}
                </h2>
                <div
                  className={`w-2 h-2 rounded-full flex-shrink-0 mt-2`}
                  style={getDotStyles(item.key)}
                ></div>
                <h2
                  className={`text-xl font-bold break-words`}
                  style={{
                    fontFamily: "Prometo Bold",
                    fontSize: "20px",
                    letterSpacing: "0.05em",
                    lineHeight: "1.0",
                    verticalAlign: "top",
                    ...getKeyStyles(item.key)
                  }}
                >
                  {item.title || "Untitled"}
                </h2>
              </div>
              <p
                className="text-gray-600 mt-4 break-words whitespace-pre-line"
                style={{
                  fontFamily: "Prometo Light",
                  fontSize: "14px",
                  letterSpacing: "0.04em",
                  lineHeight: "normal",
                }}
              >
                {item.description || "No description available"}
              </p>
            </div>
          </div>
        );
      })}
    </>
  );
};

export default PopupCards;