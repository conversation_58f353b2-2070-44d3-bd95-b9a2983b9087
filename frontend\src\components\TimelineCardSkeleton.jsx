const TimelineCardSkeleton = () => {
    return (
        <div className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
            {/* Thumbnail skeleton */}
            <div className="relative">
                <div className="w-full h-48 bg-gray-200 animate-pulse rounded-t-lg" />
            </div>
            
            <div className="p-4">
                {/* Category badge skeleton */}
                <div className="mb-2">
                    <div className="w-24 h-6 bg-gray-200 animate-pulse rounded-full" />
                </div>
                
                {/* Title skeleton */}
                <div className="w-3/4 h-6 bg-gray-200 animate-pulse rounded mb-2" />
                
                {/* Year skeleton */}
                <div className="w-20 h-4 bg-gray-200 animate-pulse rounded mb-4" />
                
                {/* Action buttons skeleton */}
                <div className="flex justify-between mt-auto">
                    <div className="w-8 h-8 bg-gray-200 animate-pulse rounded-full" />
                    <div className="w-8 h-8 bg-gray-200 animate-pulse rounded-full" />
                </div>
            </div>
        </div>
    );
};

export default TimelineCardSkeleton; 