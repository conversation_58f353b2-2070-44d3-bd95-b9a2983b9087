/* eslint-disable react/prop-types */
import { useRef, useMemo } from "react";
import * as THREE from "three";
import { use<PERSON>rame } from "@react-three/fiber";

export const FloatingParticles = ({ count = 400 }) => {
  const meshRef = useRef();
  const dummy = useMemo(() => new THREE.Object3D(), []);

  const particles = useMemo(() => {
    const positions = [];
    for (let i = 0; i < count; i++) {
      const initialX = Math.random() * 30; // Initial x from 0 to 16
      const initialY = Math.random() * 0.5 - 0.2; // Initial y from -0.3 to 0.3
      positions.push({
        initialX, // Store initial x
        initialY, // Store initial y
        x: initialX,
        y: initialY,
        z: 0,
        speedX: Math.random() * 0.0005- 0.0002, // Random speed for x: -0.003 to 0.002
        speedY: (Math.random() * 0.5 - 0.2) * 0.002, // Random speed for y: -0.0006 to 0.0006
      });
    }
    return positions;
  }, [count]);

  useFrame(() => {
    particles.forEach((p, i) => {
      // Move particle with constant speeds
      p.x += p.speedX;
      p.y += p.speedY;

      // Reset to initial position if y exceeds bounds
      if (p.y > 0.5 || p.y < -0.5) {
        p.x = p.initialX; // Reset to initial x
        p.y = p.initialY; // Reset to initial y
        // Regenerate speeds to maintain random movement
        p.speedX = Math.random() * 0.005 - 0.003;
        p.speedY = (Math.random() * 0.5 - 0.2) * 0.002;
      }

      // Bounce at x-axis boundaries by reversing speedX
      if (p.x > 30) {
        p.x = 30; // Clamp to boundary
        p.speedX = -Math.abs(p.speedX); // Reverse direction (move left)
      } else if (p.x < 0) {
        p.x = 0; // Clamp to boundary
        p.speedX = Math.abs(p.speedX); // Reverse direction (move right)
      }

      dummy.position.set(p.x, p.y, p.z);
      dummy.updateMatrix();
      meshRef.current.setMatrixAt(i, dummy.matrix);
    });

    meshRef.current.instanceMatrix.needsUpdate = true;
  });

  return (
    <instancedMesh ref={meshRef} args={[null, null, count]}>
      <sphereGeometry args={[0.003, 8, 8]} />
      <meshBasicMaterial
        color="white"
        transparent={true}
        opacity={0.8}
        depthWrite={false}
        depthTest={true}
      />
    </instancedMesh>
  );
};