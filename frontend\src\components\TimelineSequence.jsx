import { useState } from "react";
import PropTypes from "prop-types";
import { Dialog } from "@headlessui/react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { store } from "../redux/store";

const getKeyStyles = (key) => {
    const styleMap = {
        'our_journey': 'text-purple-600',
        'highlights': 'text-orange-600',
        'sustainability': 'text-green-600',
        'dark_days': 'text-red-600'
    };
    return styleMap[key] || 'text-gray-600';
};

const TimelineSequence = ({ timelines }) => {
  const [selectedTimeline, setSelectedTimeline] = useState(null);
  const [isOpen, setIsOpen] = useState(false);

  const handleTimelineClick = async (timeline) => {
    setSelectedTimeline(timeline);
    setIsOpen(true);

    // Track the click
    try {
      // Get the numeric key ID based on the timeline key string
      const keyMap = {
        'our_journey': '1',
        'highlights': '2',
        'sustainability': '3', 
        'dark_days': '4'
      };
      
      const keyId = keyMap[timeline.key];
      if (keyId) {
        // Get the authentication token from Redux store
        const state = store.getState();
        const token = state.user?.currentUser?.token;
        
        if (!token) {
          console.warn('No authentication token found');
          return;
        }

        console.log(`Tracking click for key: ${keyId}`);
        const response = await fetch(`https://miscbranding.com/api/insert-key?key=${keyId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          credentials: 'include', // Include cookies for authentication
        });
        
        if (!response.ok) {
          if (response.status === 403) {
            console.warn('Authentication failed - user not logged in');
            return;
          }
          throw new Error(`Failed to track click: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('Click tracked successfully:', data);

        /* Original code - commented out
        try {
          // Try the new API format endpoint first
          const response = await post(`${import.meta.env.VITE_API_IP}/api/insert-key`, { key: keyId });
          
          if (!response.ok) {
            // Check if authentication error
            if (response.status === 403) {
              console.warn("Authentication required for new API endpoint, falling back to public endpoint");
              throw new Error("Authentication required");
            }
            throw new Error("Failed to track click with new API endpoint");
          }
          
          const data = await response.json();
          console.log('Click tracked successfully with authenticated endpoint:', data);
        } catch (newApiError) {
          try {
            // Fallback to the previous endpoint
            console.warn("Falling back to previous endpoint:", newApiError.message);
            const response = await get(`${import.meta.env.VITE_API_IP}/keymilestone/insert?key=${keyId}`);
            
            if (!response.ok) {
              // Check if authentication error
              if (response.status === 403) {
                console.warn("Authentication required for keymilestone endpoint, falling back to public endpoint");
                throw new Error("Authentication required");
              }
              throw new Error("Failed to track click with keymilestone endpoint");
            }
            
            const data = await response.json();
            console.log('Click tracked with keymilestone endpoint:', data);
          } catch (keymilestoneError) {
            console.warn("Falling back to original endpoint:", keymilestoneError.message);
            
            // Last resort fallback to the original endpoint that doesn't require auth
            try {
              const publicResponse = await post(`${import.meta.env.VITE_API_IP}/v2/click/track/public`, { key: timeline.key });
              if (publicResponse.ok) {
                const data = await publicResponse.json();
                console.log('Click tracked with public endpoint:', data);
              }
            } catch (publicError) {
              console.error("Failed to track click with any endpoint:", publicError);
            }
          }
        }
        */
      } else {
        console.warn("Invalid key for tracking:", timeline.key);
      }
    } catch (error) {
      console.error('Error tracking click:', error);
    }
  };

  const closeModal = () => {
    setIsOpen(false);
    setSelectedTimeline(null);
  };

  return (
    <div className="relative">
      {/* Timeline Sequence */}
      <div className="space-y-8">
        {timelines.map((timeline, index) => (
          <div key={timeline._id} className="relative">
            {/* Year and Title */}
            <div className="mb-2">
              <h3 className="text-xl font-semibold text-gray-900">
                {timeline.year}
              </h3>
              <h4 className="text-lg font-medium text-gray-700">
                {timeline.title}
              </h4>
            </div>

            {/* Timeline Button */}
            <button
              onClick={() => handleTimelineClick(timeline)}
              className="w-full p-4 bg-white border-2 border-blue-500 rounded-lg shadow-sm hover:bg-blue-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              aria-label={`View details for ${timeline.title}`}
            >
              <div className="flex items-center justify-between">
                <span className="text-blue-600 font-medium">View Details</span>
                <span className="text-gray-500 text-sm">Click to expand</span>
              </div>
            </button>

            {/* Timeline Line (except for last item) */}
            {index < timelines.length - 1 && (
              <div className="absolute left-1/2 top-full w-0.5 h-8 bg-gray-300 transform -translate-x-1/2" />
            )}
          </div>
        ))}
      </div>

      {/* Modal/Popup */}
      <Dialog open={isOpen} onClose={closeModal} className="relative z-50">
        {/* Backdrop */}
        <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

        {/* Modal Panel */}
        <div className="fixed inset-0 flex items-center justify-center p-4">
          <Dialog.Panel className="w-full max-w-2xl bg-white rounded-lg shadow-xl">
            {selectedTimeline && (
              <div className="p-6">
                {/* Year */}
                <div className="flex justify-between items-start mb-2">
                  <p className={`text-lg font-medium ${getKeyStyles(selectedTimeline.key)}`}>
                    {selectedTimeline.year}
                  </p>
                  <button
                    onClick={closeModal}
                    className="text-gray-400 hover:text-gray-500"
                    aria-label="Close"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                {/* Header */}
                <Dialog.Title className={`text-2xl font-bold ${getKeyStyles(selectedTimeline.key)}`}>
                    {selectedTimeline.title}
                  </Dialog.Title>

                {/* Description */}
                <div className="prose max-w-none mb-6">
                  <p className="text-gray-600">
                    {selectedTimeline.description}
                  </p>
                </div>

                {/* Thumbnail */}
                {selectedTimeline.thumbnail && (
                  <div className="mb-6">
                    <img
                      src={`${import.meta.env.VITE_API_IP}/uploads/${selectedTimeline.thumbnail}`}
                      alt={selectedTimeline.title}
                      className="w-full h-64 object-cover rounded-lg"
                    />
                  </div>
                )}

                {/* Files (Images/Videos) */}
                {selectedTimeline.files &&
                  selectedTimeline.files.length > 0 && (
                    <div className="grid grid-cols-2 gap-4">
                      {selectedTimeline.files.map((file, index) => (
                        <div key={index} className="relative">
                          {file.type === "image" ? (
                            <img
                              src={`${import.meta.env.VITE_API_IP}/uploads/${file.path}`}
                              alt={`${selectedTimeline.title} - Image ${
                                index + 1
                              }`}
                              className="w-full h-48 object-cover rounded-lg"
                            />
                          ) : (
                            <video
                              src={`${import.meta.env.VITE_API_IP}/uploads/${file.path}`}
                              controls
                              className="w-full h-48 object-cover rounded-lg"
                            />
                          )}
                        </div>
                      ))}
                    </div>
                  )}
              </div>
            )}
          </Dialog.Panel>
        </div>
      </Dialog>
    </div>
  );
};

TimelineSequence.propTypes = {
  timelines: PropTypes.arrayOf(
    PropTypes.shape({
      _id: PropTypes.string.isRequired,
      year: PropTypes.number.isRequired,
      title: PropTypes.string.isRequired,
      description: PropTypes.string.isRequired,
      thumbnail: PropTypes.string,
      files: PropTypes.arrayOf(
        PropTypes.shape({
          path: PropTypes.string.isRequired,
          type: PropTypes.oneOf(["image", "video"]).isRequired,
        })
      ),
    })
  ).isRequired,
};

export default TimelineSequence;
