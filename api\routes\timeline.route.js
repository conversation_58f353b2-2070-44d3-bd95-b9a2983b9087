import express from 'express';
import { createTimeline, updateTimeline, deleteTimeline, deleteTimelineFile, getTimelines, getTimeline } from '../controllers/timeline.controller.js';
import { upload } from '../utils/fileUpload.js';
import { verifyToken } from '../utils/verifyUser.js';

const router = express.Router();

// Create timeline with files
router.post('/create', verifyToken, 
    upload.fields([
        { name: 'thumbnail', maxCount: 1 },
        { name: 'files', maxCount: 10 }
    ]), 
    createTimeline
);

// Get all timelines (protected - requires authentication)
router.get('/', verifyToken, getTimelines);

// Get all timelines (public - no authentication required)
router.get('/public', getTimelines);

// Get single timeline
router.get('/:id', verifyToken, getTimeline);

// Get single timeline (public - no authentication required)
router.get('/public/:id', getTimeline);

// Update timeline with new files
router.put('/update/:id', verifyToken, 
    upload.fields([
        { name: 'thumbnail', maxCount: 1 },
        { name: 'files', maxCount: 10 }
    ]), 
    updateTimeline
);

// Delete timeline and its files
router.delete('/delete/:id', verifyToken, deleteTimeline);

// Delete specific file from timeline
router.delete('/:timelineId/files/:fileId', verifyToken, deleteTimelineFile);

export default router; 