import { useEffect, useState, useCallback, useMemo, memo } from 'react';
import TimelineCard from './TimelineCard';
import TimelineCardSkeleton from './TimelineCardSkeleton';
import { useNavigate } from 'react-router-dom';
import { FaExternalLinkAlt, FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import { get, del } from '../utils/api';
import { useDebounce } from '../hooks/useDebounce';

const ITEMS_PER_PAGE = 18; // 6 rows of 3 cards each

// Memoized TimelineCard component to prevent unnecessary re-renders
const MemoizedTimelineCard = memo(TimelineCard);

const TimelineList = () => {
    const [allTimelines, setAllTimelines] = useState([]);
    const [displayedTimelines, setDisplayedTimelines] = useState([]);
    const [loading, setLoading] = useState(true);
    const [pageChanging, setPageChanging] = useState(false); // New state for page transition loading
    const [error, setError] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [initialLoadDone, setInitialLoadDone] = useState(false);
    const navigate = useNavigate();
    
    // Debounce search term to avoid too many filtering operations
    const debouncedSearch = useDebounce(searchTerm, 500);

    // Function to fetch all timelines data at once
    const fetchAllTimelines = useCallback(async () => {
        try {
            setLoading(true);
            
            const response = await get(`${import.meta.env.VITE_API_IP}/v2/timeline`);
            
            if (!response.ok) {
                throw new Error(`API request failed with status ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.success) {
                // Sort all timelines by year in ascending order
                const sortedTimelines = data.timelines.sort((a, b) => a.year - b.year);
                setAllTimelines(sortedTimelines);
            } else {
                setError(data.message || 'Failed to fetch timelines');
            }
        } catch (err) {
            console.error('Error fetching timelines:', err);
            setError(err.message || 'An error occurred while fetching data');
        } finally {
            setLoading(false);
            setInitialLoadDone(true);
        }
    }, []);

    // Initialize data
    useEffect(() => {
        fetchAllTimelines();
    }, [fetchAllTimelines]);

    // Process data when page or search changes
    useEffect(() => {
        if (allTimelines.length > 0) {
            // Show loading state during page change
            setPageChanging(true);
            
            // Simulate a small delay to ensure skeleton shows
            const processData = async () => {
                try {
                    // Filter based on search term
                    const filteredTimelines = debouncedSearch 
                        ? allTimelines.filter(timeline => 
                            timeline.title.toLowerCase().includes(debouncedSearch.toLowerCase()) || 
                            timeline.year.toString().includes(debouncedSearch)
                        )
                        : allTimelines;
                    
                    // Calculate total pages
                    const calculatedTotalPages = Math.max(1, Math.ceil(filteredTimelines.length / ITEMS_PER_PAGE));
                    setTotalPages(calculatedTotalPages);
                    
                    // Adjust current page if needed
                    if (currentPage > calculatedTotalPages) {
                        setCurrentPage(1);
                    }
                    
                    // Get the items for the current page
                    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
                    const endIndex = startIndex + ITEMS_PER_PAGE;
                    const paginatedTimelines = filteredTimelines.slice(startIndex, endIndex);
                    
                    // Add a small artificial delay to show loading state
                    // Comment this out in production if not needed
                    await new Promise(resolve => setTimeout(resolve, 300));
                    
                    setDisplayedTimelines(paginatedTimelines);
                } finally {
                    setPageChanging(false);
                }
            };
            
            processData();
        }
    }, [allTimelines, currentPage, debouncedSearch]);

    // Memoize handlers to prevent recreating functions on each render
    const handleEdit = useCallback((id) => {
        navigate(`/adminpanel/timeline/edit/${id}`);
    }, [navigate]);

    const handleDelete = useCallback(async (id) => {
        if (!window.confirm('Are you sure you want to delete this timeline?')) {
            return;
        }

        try {
            setLoading(true);
            const response = await del(`${import.meta.env.VITE_API_IP}/v2/timeline/delete/${id}`);
            if (!response.ok) {
                throw new Error(`API request failed with status ${response.status}`);
            }
            
            // Remove the deleted timeline from our local state
            setAllTimelines(prev => prev.filter(timeline => timeline._id !== id));
            alert('Timeline deleted successfully');
        } catch (err) {
            alert(err.message || 'Error deleting timeline');
            console.error('Error deleting timeline:', err);
        } finally {
            setLoading(false);
        }
    }, []);

    const handlePageChange = useCallback((newPage) => {
        if (newPage >= 1 && newPage <= totalPages) {
            // Set page changing loading state
            setPageChanging(true);
            setCurrentPage(newPage);
            window.scrollTo({ top: 0, behavior: 'smooth' }); // Smoother scrolling
        }
    }, [totalPages]);

    const handleSearch = useCallback((e) => {
        setSearchTerm(e.target.value);
        setCurrentPage(1); // Reset to first page when searching
        setPageChanging(true); // Show loading state when searching
    }, []);

    // Memoize the skeleton cards grid
    const skeletonCardsGrid = useMemo(() => (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 min-h-[500px]">
            {/* Show 18 skeleton cards (6 rows of 3) to match full page layout */}
            {[...Array(ITEMS_PER_PAGE)].map((_, index) => (
                <TimelineCardSkeleton key={index} />
            ))}
        </div>
    ), []);

    // Memoize the loading skeleton for initial load
    const initialLoadingSkeleton = useMemo(() => (
        <div className="px-20 py-6">
            <div className="flex items-center mb-4">
                <div className="w-40 h-10 bg-gray-200 animate-pulse rounded-lg" />
                <div className="w-1/2 h-10 bg-gray-200 animate-pulse rounded-lg mx-auto" />
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
                {/* Show 6 skeleton cards (2 rows of 3) during initial load */}
                {[...Array(6)].map((_, index) => (
                    <TimelineCardSkeleton key={index} />
                ))}
            </div>
        </div>
    ), []);

    // Memoize timeline cards rendering
    const timelineCardsGrid = useMemo(() => (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 min-h-[500px]">
            {displayedTimelines.map((timeline) => (
                <MemoizedTimelineCard 
                    key={timeline._id} 
                    timeline={timeline} 
                    onEdit={handleEdit} 
                    onDelete={handleDelete} 
                />
            ))}
        </div>
    ), [displayedTimelines, handleEdit, handleDelete]);

    // If still loading during initial fetch, show skeleton
    if (loading && !initialLoadDone) {
        return initialLoadingSkeleton;
    }

    // If there's an error, show error message
    if (error) {
        return (
            <div className="text-center py-4">
                <p className="text-red-600 font-medium">{error}</p>
            </div>
        );
    }

    // If data is empty but no longer loading, show empty state
    if (allTimelines.length === 0 && initialLoadDone) {
        return (
            <div className="px-20 py-6">
                <div className="flex items-center mb-4">
                    <button
                        onClick={() => window.open('/milestone-v2/', '_blank')}
                        className="flex items-center bg-blue-500 text-white rounded-lg px-4 py-2 hover:bg-blue-600 transition"
                        aria-label="Navigate to Milestone site"
                    >
                        <FaExternalLinkAlt className="mr-2" />
                        To Milestone site
                    </button>

                    <input
                        type="text"
                        placeholder="Search by title or year"
                        value={searchTerm}
                        onChange={handleSearch}
                        className="border border-gray-300 rounded-lg p-2 mx-auto w-1/2"
                    />
                </div>
                <div className="text-center py-8">
                    <p className="text-gray-500 text-lg">No timelines found. Add some timelines to get started!</p>
                </div>
            </div>
        );
    }

    return (
        <div className="px-20 py-6">
            <div className="flex items-center mb-4">
                <button
                    onClick={() => window.open('/milestone-v2/', '_blank')}
                    className="flex items-center bg-blue-500 text-white rounded-lg px-4 py-2 hover:bg-blue-600 transition"
                    aria-label="Navigate to Milestone site"
                >
                    <FaExternalLinkAlt className="mr-2" />
                    To Milestone site
                </button>

                <input
                    type="text"
                    placeholder="Search by title or year"
                    value={searchTerm}
                    onChange={handleSearch}
                    className="border border-gray-300 rounded-lg p-2 mx-auto w-1/2"
                />
            </div>

            {displayedTimelines.length === 0 && !pageChanging ? (
                <div className="text-center py-8">
                    <p className="text-gray-500 text-lg">No timelines found matching your search criteria</p>
                </div>
            ) : (
                <>
                    {/* Show skeleton during page changes or when loading */}
                    {(loading || pageChanging) ? skeletonCardsGrid : timelineCardsGrid}

                    {/* Pagination Controls */}
                    <div className="flex justify-center items-center mt-8 gap-4">
                        <button
                            onClick={() => handlePageChange(currentPage - 1)}
                            disabled={currentPage === 1 || loading || pageChanging}
                            className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-blue-600 transition"
                            aria-label="Previous page"
                        >
                            <FaChevronLeft className="mr-2" />
                            Previous
                        </button>
                        
                        <div className="flex items-center gap-2">
                            <span className="text-gray-600">
                                Page {currentPage} of {totalPages}
                            </span>
                            <span className="text-gray-400">
                                ({allTimelines.length} items)
                            </span>
                        </div>

                        <button
                            onClick={() => handlePageChange(currentPage + 1)}
                            disabled={currentPage === totalPages || loading || pageChanging}
                            className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-blue-600 transition"
                            aria-label="Next page"
                        >
                            Next
                            <FaChevronRight className="ml-2" />
                        </button>
                    </div>
                </>
            )}
        </div>
    );
};

export default TimelineList;
