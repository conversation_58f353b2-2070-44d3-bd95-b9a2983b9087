import Click from '../models/click.model.js';
import Tracking from '../models/tracking.model.js';

export const getClickCountByKey = async (req, res, next) => {
    try {
        // Get key from either query params (GET) or request body (POST)
        const key = req.query.key || (req.body && req.body.key);
        
        // Map query parameter values to the corresponding keys in database
        const keyMap = {
            '1': 'our_journey',
            '2': 'highlights',
            '3': 'sustainability',
            '4': 'dark_days'
        };
        
        const dbKey = keyMap[key];
        
        if (!dbKey) {
            if (req.simpleResponse) {
                return res.status(400).send('0');  // Return 0 for invalid keys in simple response mode
            }
            return res.status(400).json({
                status: "error",
                message: 'Invalid key parameter. Must be 1, 2, 3, or 4.'
            });
        }
        
        // Check if tracking is enabled
        const tracking = await Tracking.findOne();
        
        // Determine if this is a read-only request or if we should increment the counter
        // Only increment if this is not explicitly a read-only request and tracking is enabled
        const shouldIncrement = !req.isReadOnly && tracking && tracking.isEnabled;
        
        if (shouldIncrement) {
            // Find and update the click count for the given key
            const click = await Click.findOneAndUpdate(
                { key: dbKey },
                { $inc: { count: 1 } },
                { upsert: true, new: true }
            );
            
            if (req.simpleResponse) {
                return res.status(200).send(click.count.toString());
            }
            
            return res.status(200).json({
                status: "success",
                data: {
                    key: parseInt(key),
                    value: click.count,
                    created_at: click.createdAt,
                    updated_at: click.updatedAt,
                    id: click._id
                }
            });
        } else {
            // Just get the current count without incrementing
            const click = await Click.findOne({ key: dbKey });
            const count = click ? click.count : 0;
            
            if (req.simpleResponse) {
                return res.status(200).send(count.toString());
            }
            
            return res.status(200).json({
                status: "success",
                data: click ? {
                    key: parseInt(key),
                    value: click.count,
                    created_at: click.createdAt,
                    updated_at: click.updatedAt,
                    id: click._id
                } : {
                    key: parseInt(key),
                    value: 0,
                    created_at: null,
                    updated_at: null,
                    id: null
                }
            });
        }
    } catch (error) {
        if (req.simpleResponse) {
            return res.status(500).send('0');  // Return 0 on error in simple response mode
        }
        return res.status(500).json({
            status: "error",
            message: error.message
        });
    }
};

export const getAllClickCounts = async (req, res, next) => {
    try {
        // Get all click counts
        const clicks = await Click.find();
        
        // Create a map of all possible keys with default count 0
        const counts = {
            our_journey: 0,
            highlights: 0,
            sustainability: 0,
            dark_days: 0
        };

        // Update counts with actual values from database
        clicks.forEach(click => {
            if (counts.hasOwnProperty(click.key)) {
                counts[click.key] = click.count;
            }
        });

        // Check if tracking is enabled
        const tracking = await Tracking.findOne();
        const isTrackingEnabled = tracking && tracking.isEnabled;

        // Transform to the requested format
        const data = Object.entries(counts).map(([key, value]) => {
            const keyNumber = {
                'our_journey': 1, 
                'highlights': 2,
                'sustainability': 3,
                'dark_days': 4
            }[key];
            
            // Find the click document to get its ID and timestamps
            const clickDoc = clicks.find(c => c.key === key);
            
            return {
                key: keyNumber,
                value: value,
                created_at: clickDoc ? clickDoc.createdAt : null,
                updated_at: clickDoc ? clickDoc.updatedAt : null,
                id: clickDoc ? clickDoc._id : null
            };
        });

        res.status(200).json({
            status: "success",
            data: data,
            tracking_enabled: isTrackingEnabled
        });
    } catch (error) {
        res.status(500).json({
            status: "error",
            message: error.message
        });
    }
}; 