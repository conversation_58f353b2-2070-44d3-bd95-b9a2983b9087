import Click from '../models/click.model.js';
import Tracking from '../models/tracking.model.js';
import { errorHandler } from '../utils/error.js';

export const trackClick = async (req, res, next) => {
    try {
        const { key } = req.body;

        // Check if tracking is enabled
        const tracking = await Tracking.findOne();
        if (!tracking || !tracking.isEnabled) {
            return res.status(200).json({
                success: true,
                message: 'Tracking is disabled'
            });
        }

        // Find and update the click count for the given key
        const click = await Click.findOneAndUpdate(
            { key },
            { $inc: { count: 1 } },
            { upsert: true, new: true }
        );

        res.status(200).json({
            success: true,
            click
        });
    } catch (error) {
        next(error);
    }
};

export const getClickCounts = async (req, res, next) => {
    try {
        // Get all click counts
        const clicks = await Click.find();
        
        // Create a map of all possible keys with default count 0
        const counts = {
            our_journey: 0,
            highlights: 0,
            sustainability: 0,
            dark_days: 0
        };

        // Update counts with actual values from database
        clicks.forEach(click => {
            counts[click.key] = click.count;
        });

        res.status(200).json({
            success: true,
            counts
        });
    } catch (error) {
        next(error);
    }
}; 