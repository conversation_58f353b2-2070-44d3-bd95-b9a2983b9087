import User from "../models/user.model.js";
import bcryptjs from "bcryptjs";
import { errorHandler } from "../utils/error.js";
import jwt from "jsonwebtoken";

export const signup = async (req, res, next) => {
    const { username, email, password } = req.body;
    const hashedPassword = bcryptjs.hashSync(password, 10);
    const newUser = new User({ username, email, password: hashedPassword });
    try {
        await newUser.save();
        res.status(201).json("User created successfully");
    }
    catch (error) {
        next(error);
    }
};

export const signin = async (req, res, next) => {
    const { username, password } = req.body;
    try {
        const validUser = await User.findOne({ username });
        if (!validUser) return next(errorHandler(404, "User not found!"));
        const validPassword = bcryptjs.compareSync(password, validUser.password);
        if (!validPassword) return next(errorHandler(401, "Wrong credentials!"));
        const token = jwt.sign({ id: validUser._id }, process.env.JWT_SECRET);
        const { password: pass, ...rest } = validUser._doc;
        
        // Set cookie with appropriate flags for cross-domain usage
        res.cookie('access_token', token, { 
            httpOnly: true,
            sameSite: 'none', // For cross-site requests
            secure: true,     // Required with sameSite=none
            maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
        }).status(200).json({ token, user: rest });
    } catch (error) {
        next(error);
    }
}

export const signout = async (req, res, next) => {
    try {
          res.clearCookie('access_token', {
              httpOnly: true,
              sameSite: 'none',
              secure: true
          });
          res.status(200).json("User has been logged out");
    } catch (error) {
       next(errorHandler(500, "Failed to logout"));
    }
}