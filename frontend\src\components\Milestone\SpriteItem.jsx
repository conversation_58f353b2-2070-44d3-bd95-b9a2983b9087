/* eslint-disable react/prop-types */
import { useRef, useState } from "react";
import { Html } from "@react-three/drei";
import { CircleLoader, WaveCircles } from "./LoadingAnimation";
import { post } from "../../utils/api";

export const SpriteItem = ({ item, index, openCards, setOpenCards, userData }) => {
  const [isLoading, setIsLoading] = useState(false);
  const buttonRef = useRef(null);

  const formatTitle = (title) => {
    if (!title) return "";
    const words = title.split(" ");
    const totalWords = words.length;

    if (totalWords <= 8) {
      const midpoint = Math.ceil(totalWords / 3);
      const firstLine = words.slice(0, midpoint).join(" ");
      const secondLine = words.slice(midpoint).join(" ");
      return `${firstLine}\n${secondLine}`;
    } else {
      const wordsPerLine = Math.ceil(totalWords / 4);
      const firstLine = words.slice(0, wordsPerLine).join(" ");
      const secondLine = words.slice(wordsPerLine, wordsPerLine * 4).join(" ");
      const thirdLine = words.slice(wordsPerLine * 4).join(" ");
      return `${firstLine}\n${secondLine}\n${thirdLine}`;
    }
  };

  const handleClick = async () => {
    // Check if this card is already open
    const isCardOpen = openCards.some((card) => card.index === index);
    
    // If there are already 3 cards open and this is not one of them, prevent opening
    if (openCards.length >= 3 && !isCardOpen) {
      return;
    }

    setIsLoading(true);

    if (isCardOpen) {
      setOpenCards((prev) => {
        const newCards = prev.filter((card) => card.index !== index);
        return newCards;
      });
      setIsLoading(false);
      return;
    }

    // Get button position to determine initial preferred position
    const button = buttonRef.current;
    if (button) {
      const rect = button.getBoundingClientRect();
      const screenWidth = window.innerWidth;
      const buttonCenter = rect.left + (rect.width / 2);
      const leftThreshold = screenWidth * 0.33;
      const rightThreshold = screenWidth * 0.66;

      // Determine preferred position based on click location
      let preferredPosition;
      if (buttonCenter < leftThreshold) {
        preferredPosition = "left";
      } else if (buttonCenter > rightThreshold) {
        preferredPosition = "right";
      } else {
        preferredPosition = "center";
      }

      setOpenCards((prev) => {
        // For rapid consecutive clicks, we need to work with the latest state
        // Get the current positions of cards
        const usedPositions = prev.map(card => card.position);
        
        // Define the possible positions in order of proximity based on preferred position
        let positionPriority;
        if (preferredPosition === "left") {
          positionPriority = ["left", "center", "right"];
        } else if (preferredPosition === "right") {
          positionPriority = ["right", "center", "left"];
        } else {
          positionPriority = ["center", "left", "right"];
        }
        
        // Find the first available position based on priority
        const finalPosition = positionPriority.find(pos => !usedPositions.includes(pos)) || preferredPosition;
        
        // Add the new card with its position
        return [...prev, { index, position: finalPosition }];
      });
        
      setTimeout(() => {
        setIsLoading(false);
      }, 1000);
    }

    if (item.key) {
      try {
        const keyMap = {
          'our_journey': '1',
          'highlights': '2',
          'sustainability': '3', 
          'dark_days': '4'
        };
        
        const keyId = keyMap[item.key];
        if (keyId) {
          // console.log(`Tracking click for key: ${keyId}`);
          const response = await fetch(`https://miscbranding.com/api/insert-key?key=${keyId}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
          });
          
          if (!response.ok) {
            throw new Error(`Failed to track click: ${response.status}`);
          }
          
          // const data = await response.json();
          // console.log('Click tracked successfully:', data);

          // Additional tracking code
          try {
            // console.log(`Attempting to track click for key: ${keyId}`);
            let apiUrl = `${import.meta.env.VITE_API_IP}/keymilestone/increment`;
            // console.log(`Making request to: ${apiUrl}`);
            
            const response = await post(apiUrl, { key: keyId });
                        
            if (!response.ok) {
              const errorData = await response.text();
              console.error(`Error response: ${errorData}`);
              throw new Error(`Failed to track click: ${response.status} ${errorData}`);
            }

            // const data = await response.json();
            // console.log('Click tracked successfully:', data);
          } catch (newEndpointError) {
            console.warn("Falling back to original endpoint:", newEndpointError.message);
            
            await post(`${import.meta.env.VITE_API_IP}/v2/click/track/public`, { key: item.key });
          }
        } else {
          console.warn("Invalid key for tracking:", item.key);
        }
      } catch (error) {
        console.error("Error tracking click:", error);
      }
    } else {
      console.warn("No key provided for click tracking:", item);
    }

    setTimeout(() => {
      setIsLoading(false);
    }, 2000);
  };

  return (
    <group userData={userData}>
      <Html className="-translate-y-1/2 w-40 h-43.5 z-20" center>
        <div className="flex flex-col items-center justify-center">
          <div
            className="text-white text-[24px] font-bold"
            style={{
              fontFamily: "Prometo Xbold",
              letterSpacing: "0px",
            }}
          >
            {item.year?.toString() || "N/A"}
          </div>

          <div
            className="text-white text-center mt-1 mb-2 h-13 w-48 flex items-center justify-center"
            style={{
              fontFamily: "Prometo Medium",
              fontSize: "18px",
              lineHeight: "1.3",
              letterSpacing: "0px",
            }}
          >
            {formatTitle(item.title)}
          </div>

          <div className="flex flex-col items-center relative">
            <div
              className="w-0.5 h-[76px] bg-white"
              style={{
                position: "relative",
              }}
            ></div>

            <div className="relative w-12 h-11 mt-[-15px]">
              <WaveCircles />
              <button
                id={`button-${index}`}
                ref={buttonRef}
                className={`w-12 h-11 rounded-full border-0 border-white transition-all flex items-center justify-center relative z-20 ${
                  openCards.length >= 3 && !openCards.some(card => card.index === index)
                    ? "opacity-50 cursor-not-allowed"
                    : "hover:scale-110"
                }`}
                onClick={handleClick}
                disabled={openCards.length >= 3 && !openCards.some(card => card.index === index)}
              >
                {isLoading ? (
                  <CircleLoader />
                ) : (
                  <div className={`w-3 h-3 bg-white rounded-full ${
                    openCards.some(card => card.index === index) ? "scale-125" : ""
                  }`}></div>
                )}
              </button>
            </div>
          </div>
        </div>
      </Html>
    </group>
  );
};