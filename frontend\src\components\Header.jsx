import { Link } from "react-router-dom";
import { useSelector } from "react-redux";

const Header = () => {
  const { currentUser } = useSelector((state) => state.user);

  return (
    <header className="bg-white shadow-md sticky top-0 z-10">
      <div className="flex justify-between items-center max-w-6xl mx-auto p-4">
        <Link to="/">
          <h1 className="font-bold text-lg sm:text-2xl text-gray-800">
            <span className="text-blue-600">Admin</span>
            <span className="text-gray-800">Panel</span>
          </h1>
        </Link>
        <div className="flex items-center space-x-4">
          {currentUser ? (
            <div className="flex items-center space-x-2">
              <span className="text-gray-700">Hi,</span>
              <Link
                to="/adminpanel/profile"
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                {currentUser.user.username}
              </Link>
            </div>
          ) : (
            <div className="flex space-x-4">
              <Link
                to="/adminpanel/sign-in"
                className="text-gray-700 hover:text-blue-600 font-medium"
              >
                Sign In
              </Link>
              {/* <Link
                to="/sign-up"
                className="text-gray-700 hover:text-blue-600 font-medium"
              >
                Sign Up
              </Link> */}
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;
