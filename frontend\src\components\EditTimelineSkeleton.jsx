const EditTimelineSkeleton = () => {
    return (
        <div className="max-w-2xl mx-auto p-6">
            {/* Title Skeleton */}
            <div className="w-48 h-8 bg-gray-200 animate-pulse rounded mb-6" />
            
            <div className="space-y-6">
                {/* Year Input Skeleton */}
                <div>
                    <div className="w-16 h-5 bg-gray-200 animate-pulse rounded mb-2" />
                    <div className="w-full h-10 bg-gray-200 animate-pulse rounded" />
                </div>

                {/* Category Select Skeleton */}
                <div>
                    <div className="w-24 h-5 bg-gray-200 animate-pulse rounded mb-2" />
                    <div className="w-full h-10 bg-gray-200 animate-pulse rounded" />
                </div>

                {/* Title Input Skeleton */}
                <div>
                    <div className="w-16 h-5 bg-gray-200 animate-pulse rounded mb-2" />
                    <div className="w-full h-10 bg-gray-200 animate-pulse rounded" />
                </div>

                {/* Description Textarea Skeleton */}
                <div>
                    <div className="w-28 h-5 bg-gray-200 animate-pulse rounded mb-2" />
                    <div className="w-full h-32 bg-gray-200 animate-pulse rounded" />
                </div>

                {/* Caption Input Skeleton */}
                <div>
                    <div className="w-20 h-5 bg-gray-200 animate-pulse rounded mb-2" />
                    <div className="w-full h-10 bg-gray-200 animate-pulse rounded" />
                </div>

                {/* Thumbnail Skeleton */}
                <div>
                    <div className="w-24 h-5 bg-gray-200 animate-pulse rounded mb-2" />
                    <div className="w-full h-48 bg-gray-200 animate-pulse rounded-lg" />
                </div>

                {/* Files Section Skeleton */}
                <div>
                    <div className="w-16 h-5 bg-gray-200 animate-pulse rounded mb-2" />
                    {/* Grid of file previews */}
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                        {[...Array(3)].map((_, index) => (
                            <div key={index} className="w-full h-32 bg-gray-200 animate-pulse rounded-lg" />
                        ))}
                    </div>
                    {/* Upload area skeleton */}
                    <div className="w-full h-40 bg-gray-100 animate-pulse rounded-lg border-2 border-dashed border-gray-300" />
                </div>

                {/* Buttons Skeleton */}
                <div className="flex gap-4">
                    <div className="w-32 h-10 bg-gray-200 animate-pulse rounded" />
                    <div className="w-24 h-10 bg-gray-200 animate-pulse rounded" />
                </div>
            </div>
        </div>
    );
};

export default EditTimelineSkeleton; 