import express from 'express';
import mongoose from 'mongoose';
import dotenv from 'dotenv';
import cookieParser from 'cookie-parser';
import userRouter from './routes/user.route.js';
import authRouter from './routes/auth.route.js';
import timelineRouter from './routes/timeline.route.js';
import clickRouter from './routes/click.route.js';
import trackingRouter from './routes/tracking.route.js';
import keymilestoneRouter from './routes/keymilestone.route.js';
import path from 'path';
import cors from 'cors'; // if using ES Modules (with "type": "module" in package.json)
import { fileURLToPath } from 'url';
import { verifyToken } from './utils/verifyUser.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config();

mongoose
    .connect(process.env.MONGO)
    .then(() => console.log("Connected to MongoDB"))
    .catch((err) => console.log(err));

const app = express();

app.use(express.json());
app.use(cookieParser());

// Update CORS configuration to allow credentials with specific origins
app.use(cors({
    origin: [
      'http://localhost:5173', // Local development
      'http://localhost:3000',
      'https://miscbranding.com',
      'https://www.miscbranding.com',
      'https://api.miscbranding.com',
      'https://fiverr-milestone-mongodb.vercel.app',
      process.env.FRONTEND_URL // In case you add this to your .env
    ].filter(Boolean), // Remove any undefined/null values
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept']
}));

// Configure static file serving with absolute path
const uploadsPath = '/home/<USER>/backend-deploy/uploads';
// const uploadsPath = '/var/www/html/uploads';
// const uploadsPath = path.join(__dirname, '../uploads');
console.log('Serving uploads from:', uploadsPath);
app.use('/uploads', express.static(uploadsPath));

// API routes
app.use('/v2/user', userRouter);
app.use('/v2/auth', authRouter);
app.use('/auth', authRouter);      // ✅ New simplified path
app.use('/v2/timeline', timelineRouter);
app.use('/v2/click', clickRouter);
app.use('/v2/tracking', trackingRouter);
app.use('/keymilestone', keymilestoneRouter);

// New API route for insert-key endpoint
// app.post('/api/insert-key', verifyToken, (req, res, next) => {
//     // Set CORS headers explicitly for this endpoint
//     res.header('Access-Control-Allow-Origin', 'https://miscbranding.com');
//     res.header('Access-Control-Allow-Methods', 'POST');
//     res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
//     res.header('Access-Control-Allow-Credentials', 'true');
    
//     // Handle preflight request
//     if (req.method === 'OPTIONS') {
//         return res.status(200).end();
//     }
    
//     req.isReadOnly = false; // Set to false to increment the counter
//     import('./controllers/keymilestone.controller.js')
//         .then(module => {
//             module.getClickCountByKey(req, res, next);
//         })
//         .catch(err => next(err));
// });

// Debug route to check if server is responding
app.get('/api-health', (req, res) => {
    res.status(200).json({ message: 'API is running' });
});

// Add this line in api/index.js right after other routes
app.get('/auth/test', (req, res) => {
  res.status(200).json({ message: 'Auth test route working' });
});

// 404 handler for API routes
app.use((req, res, next) => {
    if (req.path.startsWith('/v2/') || req.path.startsWith('/keymilestone/') || req.path.startsWith('/auth/')) {
        return res.status(404).json({
            success: false,
            message: `API endpoint not found: ${req.method} ${req.path}`
        });
    }
    next();
});

// Error handler
app.use((err, req, res, next) => {
    console.error(err.stack);
    const statusCode = err.statusCode || 500;
    const message = err.message || "Internal server error";
    return res.status(statusCode).json({
        success: false,
        statusCode, 
        message, 
    });
});

// Start the server
app.listen(process.env.PORT || 4003, () => {
    console.log(`Server is running on port ${process.env.PORT || 4003}!!`);
});