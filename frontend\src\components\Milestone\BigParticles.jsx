/* eslint-disable react/prop-types */
import { useRef, useMemo } from "react";
import { useFrame } from "@react-three/fiber";
import * as THREE from "three";

const BigParticles = ({ count = 60 }) => {
  const meshRef = useRef();
  const dummy = useMemo(() => new THREE.Object3D(), []);

  // Generate random positions, scales, colors, and directions for the particles
  const particles = useMemo(() => {
    const temp = [];
    for (let i = 0; i < count; i++) {
      const x = Math.random() * 26 - 10; // Spread across X-axis
      const y = Math.random() * 0.4 - 0.2; // Spread across Y-axis
      const z = Math.random() * 0.6 - 0.4; // Spread across Z-axis
      const scale = Math.random() * 0.1 + 0.13; // Random scale
      const speed = Math.random() * 0.0005 - 0.0002; // Random speed for movement
      const direction = Math.random() > 0.5 ? 1 : -1; // Random direction (left or right)
      const color = new THREE.Color(1, 1, 1);
      temp.push({ x, y, z, scale, speed, direction, color });
    }
    return temp;
  }, [count]);

  // Animation for movement in both directions
  useFrame(() => {
    particles.forEach((particle, i) => {
      // Move in the particle's designated direction
      particle.x += particle.speed * particle.direction;
      
      // Reset position when particles go too far in either direction
      if (particle.direction > 0 && particle.x > 15) {
        particle.x = -1; // Reset to left if moving right and goes too far
      } else if (particle.direction < 0 && particle.x < -15) {
        particle.x = 1; // Reset to right if moving left and goes too far
      }

      // Update position and scale for each particle
      dummy.position.set(particle.x, particle.y, particle.z);
      dummy.scale.set(particle.scale, particle.scale, particle.scale);
      dummy.updateMatrix();
      meshRef.current.setMatrixAt(i, dummy.matrix);
      meshRef.current.setColorAt(i, particle.color);
    });
    meshRef.current.instanceMatrix.needsUpdate = true;
    meshRef.current.instanceColor.needsUpdate = true;
  });

  // Create a spherical geometry for the particles with a glowing material
  const geometry = useMemo(() => new THREE.SphereGeometry(0.5, 32, 32), []);
  const material = useMemo(
    () =>
      new THREE.MeshBasicMaterial({
        transparent: true,
        opacity: 0.03,
        depthWrite: false,
        blending: THREE.AdditiveBlending, // For glowing effect
      }),
    []
  );

  return (
    <instancedMesh ref={meshRef} args={[geometry, material, count]}>
      <instancedBufferAttribute attach="instanceColor" args={[new Float32Array(count * 3), 3]} />
    </instancedMesh>
  );
};

export default BigParticles;