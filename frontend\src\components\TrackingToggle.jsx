import { useState, useEffect } from 'react';
import { Switch } from '@headlessui/react';

const TrackingToggle = () => {
    const [enabled, setEnabled] = useState(false);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        fetchTrackingStatus();
    }, []);

    const fetchTrackingStatus = async () => {
        try {
            // const response = await fetch('/v2/tracking/status');
            const response = await fetch(`${import.meta.env.VITE_API_IP}/v2/tracking/status`);
            const data = await response.json();
            if (data.success) {
                setEnabled(data.isEnabled);
            }
        } catch (error) {
            console.error('Error fetching tracking status:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleToggle = async () => {
        try {
            // const response = await fetch('/v2/tracking/status', {
            const response = await fetch(`${import.meta.env.VITE_API_IP}/v2/tracking/status`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify({ isEnabled: !enabled })
            });
            const data = await response.json();
            if (data.success) {
                setEnabled(data.isEnabled);
            }
        } catch (error) {
            console.error('Error updating tracking status:', error);
        }
    };

    if (loading) {
        return <div className="animate-pulse h-6 w-12 bg-gray-200 rounded-full" />;
    }

    return (
        <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Click Tracking:</span>
            <Switch
                checked={enabled}
                onChange={handleToggle}
                className={`${
                    enabled ? 'bg-blue-600' : 'bg-gray-200'
                } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
            >
                <span
                    className={`${
                        enabled ? 'translate-x-6' : 'translate-x-1'
                    } inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
                />
            </Switch>
        </div>
    );
};

export default TrackingToggle; 