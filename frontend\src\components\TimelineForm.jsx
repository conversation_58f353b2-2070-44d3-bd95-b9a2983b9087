import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { post } from '../utils/api';

const TimelineForm = () => {
    const { currentUser } = useSelector((state) => state.user);
    const navigate = useNavigate();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [files, setFiles] = useState([]);
    const [thumbnail, setThumbnail] = useState(null);
    const [thumbnailPreview, setThumbnailPreview] = useState(null);
    const [formData, setFormData] = useState({
        year: '',
        key: ['our_journey'],
        title: '',
        description: '',
        caption: '',
    });

    // Check if user is authenticated
    useEffect(() => {
        if (!currentUser) {
            navigate('/adminpanel/sign-in');
            toast.error('Please sign in to access this page');
        }
    }, [currentUser, navigate]);

    const handleChange = (e) => {
        setFormData({
            ...formData,
            [e.target.id]: e.target.value,
        });
    };

    const handleFileChange = (e) => {
        const selectedFiles = Array.from(e.target.files);
        setFiles(selectedFiles);
    };

    const handleThumbnailChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            setThumbnail(file);
            setThumbnailPreview(URL.createObjectURL(file));
        }
    };

    // Add cleanup for thumbnail preview URL
    useEffect(() => {
        return () => {
            if (thumbnailPreview) {
                URL.revokeObjectURL(thumbnailPreview);
            }
        };
    }, [thumbnailPreview]);

    const handleCheckboxChange = (e) => {
        const { value } = e.target;
        setFormData((prev) => ({
            ...prev,
            key: [value], // Set the selected category as the only item in the array
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        try {
            setLoading(true);
            setError(null);

            const formDataToSend = new FormData();
            formDataToSend.append('title', formData.title);
            formDataToSend.append('description', formData.description);
            formDataToSend.append('caption', formData.caption);
            formDataToSend.append('year', formData.year);
            formDataToSend.append('key', formData.key);

            // Append thumbnail as a separate field
            if (thumbnail) {
                formDataToSend.append('thumbnail', thumbnail);
            }

            // Append other files
            files.forEach((file) => {
                formDataToSend.append('files', file);
            });

            const res = await post(`${import.meta.env.VITE_API_IP}/v2/timeline/create`, formDataToSend);
            const data = await res.json();

            if (!res.ok) {
                throw new Error(data.message || 'Error creating timeline');
            }

            toast.success('Timeline created successfully!');
            navigate('/adminpanel/event');
        } catch (error) {
            setError(error.message);
            toast.error(error.message);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="max-w-4xl mx-auto p-6">
            <h1 className="text-3xl font-semibold text-center my-7">Add Event</h1>
            <form onSubmit={handleSubmit} className="flex flex-col gap-4">
                <div className="flex flex-col gap-2">
                    <label htmlFor="title" className="text-sm font-medium text-gray-700">Title</label>
                    <input
                        type="text"
                        id="title"
                        value={formData.title}
                        onChange={handleChange}
                        className="p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                    />
                </div>

                <div className="flex flex-col gap-2">
                    <label htmlFor="thumbnail" className="text-sm font-medium text-gray-700">Thumbnail</label>
                    <div className="flex items-center justify-center w-full">
                        {thumbnailPreview ? (
                            <div className="relative w-full">
                                <img
                                    src={thumbnailPreview}
                                    alt="Thumbnail preview"
                                    className="w-full h-48 object-cover rounded-lg"
                                />
                                <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-black bg-opacity-50 rounded-lg">
                                    <label htmlFor="thumbnail" className="cursor-pointer flex items-center justify-center px-4 py-2 bg-white rounded-md shadow-sm">
                                        <svg className="w-5 h-5 mr-2 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                        </svg>
                                        Change Image
                                    </label>
                                </div>
                            </div>
                        ) : (
                            <label htmlFor="thumbnail" className="flex flex-col items-center justify-center w-full h-48 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-all">
                                <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                    <svg className="w-8 h-8 mb-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                                        <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                                    </svg>
                                    <p className="mb-2 text-sm text-gray-500"><span className="font-semibold">Click to upload thumbnail</span></p>
                                    <p className="text-xs text-gray-500">PNG, JPG or JPEG (MAX. 800x400px)</p>
                                </div>
                            </label>
                        )}
                        <input
                            type="file"
                            id="thumbnail"
                            onChange={handleThumbnailChange}
                            accept="image/*"
                            className="hidden"
                            required
                        />
                    </div>
                </div>

                <div className="flex flex-col gap-2">
                    <label htmlFor="description" className="text-sm font-medium text-gray-700">Description</label>
                    <textarea
                        id="description"
                        value={formData.description}
                        onChange={handleChange}
                        className="p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 h-32 whitespace-pre-line"
                        required
                    />
                </div>

                <div className="flex flex-col gap-2">
                    <label htmlFor="caption" className="text-sm font-medium text-gray-700">Caption</label>
                    <input
                        type="text"
                        id="caption"
                        value={formData.caption}
                        onChange={handleChange}
                        className="p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                </div>

                <div className="flex flex-col gap-2">
                    <label className="text-sm font-medium text-gray-700">Category</label>
                    <div className="flex flex-col gap-2">
                        <label>
                            <input
                                type="radio"
                                value="our_journey"
                                checked={formData.key.includes('our_journey')}
                                onChange={handleCheckboxChange}
                                className="mr-2"
                            />
                            Our Journey
                        </label>
                        <label>
                            <input
                                type="radio"
                                value="highlights"
                                checked={formData.key.includes('highlights')}
                                onChange={handleCheckboxChange}
                                className="mr-2"
                            />
                            Highlights
                        </label>
                        <label>
                            <input
                                type="radio"
                                value="sustainability"
                                checked={formData.key.includes('sustainability')}
                                onChange={handleCheckboxChange}
                                className="mr-2"
                            />
                            Sustainability
                        </label>
                        <label>
                            <input
                                type="radio"
                                value="dark_days"
                                checked={formData.key.includes('dark_days')}
                                onChange={handleCheckboxChange}
                                className="mr-2"
                            />
                            Dark Days
                        </label>
                    </div>
                </div>

                <div className="flex flex-col gap-2">
                    <label htmlFor="year" className="text-sm font-medium text-gray-700">Year</label>
                    <input
                        type="number"
                        id="year"
                        value={formData.year}
                        onChange={handleChange}
                        className="p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                    />
                </div>

                <div className="flex flex-col gap-2">
                    <label htmlFor="files" className="text-sm font-medium text-gray-700">Files (Images/Videos)</label>
                    <div className="flex items-center justify-center w-full">
                        <label htmlFor="files" className="flex flex-col items-center justify-center w-full h-40 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-all">
                            <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                <svg className="w-8 h-8 mb-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                                </svg>
                                <p className="mb-2 text-sm text-gray-500"><span className="font-semibold">Click to upload files</span> or drag and drop</p>
                                <p className="text-xs text-gray-500">Images (PNG, JPG) or Videos (MP4, WebM)</p>
                                <p className="text-xs text-gray-500 mt-1">Maximum size per file: 50MB</p>
                            </div>
                            <input
                                type="file"
                                id="files"
                                onChange={handleFileChange}
                                multiple
                                accept="image/*,video/*"
                                className="hidden"
                            />
                        </label>
                    </div>
                </div>

                {files.length > 0 && (
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        {files.map((file, index) => (
                            <div key={index} className="relative group">
                                {file.type.startsWith('image/') ? (
                                    <img
                                        src={URL.createObjectURL(file)}
                                        alt={`Preview ${index + 1}`}
                                        className="w-full h-32 object-cover rounded-lg"
                                    />
                                ) : (
                                    <video
                                        src={URL.createObjectURL(file)}
                                        className="w-full h-32 object-cover rounded-lg"
                                        controls
                                    />
                                )}
                                <button
                                    type="button"
                                    onClick={() => {
                                        setFiles(files.filter((_, i) => i !== index));
                                    }}
                                    className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                    </svg>
                                </button>
                            </div>
                        ))}
                    </div>
                )}

                <button
                    type="submit"
                    disabled={loading}
                    className="bg-blue-600 text-white p-3 rounded-lg uppercase hover:bg-blue-700 disabled:opacity-50"
                >
                    {loading ? 'Creating...' : 'Create Timeline'}
                </button>

                {error && <p className="text-red-600 text-center">{error}</p>}
            </form>
        </div>
    );
};

export default TimelineForm; 