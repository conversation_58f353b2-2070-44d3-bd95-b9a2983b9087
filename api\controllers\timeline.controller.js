import Timeline from '../models/timeline.model.js';
import { handleFileUpload, deleteFiles } from '../utils/fileUpload.js';
import { errorHandler } from '../utils/error.js';

export const createTimeline = async (req, res, next) => {
    try {
        const { year, key, title, description, caption } = req.body;

        console.log('Received data:', req.body);
        console.log('Received files:', req.files);

        if (!req.files || !req.files.thumbnail) {
            return next(errorHandler(400, 'Thumbnail is required'));
        }

        // Get the thumbnail file
        const thumbnailFile = req.files.thumbnail[0];
        const thumbnailPath = thumbnailFile.filename;

        // Process additional files if any
        let files = [];
        if (req.files.files) {
            files = req.files.files.map(file => ({
                path: file.filename,
                type: file.mimetype.startsWith('image/') ? 'image' : 'video',
                name: file.originalname,
                size: file.size,
                mimeType: file.mimetype
            }));
        }

        const newTimeline = new Timeline({
            year,
            key,
            title,
            description,
            caption,
            thumbnail: thumbnailPath,
            files
        });

        const savedTimeline = await newTimeline.save();
        res.status(201).json({
            success: true,
            message: 'Timeline created successfully',
            timeline: savedTimeline
        });
    } catch (error) {
        console.error('Create timeline error:', error);
        next(error);
    }
};

export const getTimeline = async (req, res, next) => {
    try {
        const timeline = await Timeline.findById(req.params.id);
        if (!timeline) {
            return next(errorHandler(404, 'Timeline not found'));
        }
        res.status(200).json({ success: true, timeline });
    } catch (error) {
        next(error);
    }
};

export const updateTimeline = async (req, res, next) => {
    try {
        const timeline = await Timeline.findById(req.params.id);
        if (!timeline) {
            return next(errorHandler(404, 'Timeline not found'));
        }

        const { year, key, title, description, caption } = req.body;
        
        // Create update object
        const updateObj = {
            year,
            key,
            title,
            description,
            caption,
        };

        // Handle thumbnail update
        if (req.files && req.files.thumbnail && req.files.thumbnail[0]) {
            // Delete old thumbnail if exists
            if (timeline.thumbnail) {
                await deleteFiles([timeline.thumbnail]);
            }
            updateObj.thumbnail = req.files.thumbnail[0].filename;
        }

        // Handle new file uploads if any
        if (req.files && req.files.files) {
            const newFiles = req.files.files.map(file => ({
                path: file.filename,
                type: file.mimetype.startsWith('image/') ? 'image' : 'video',
                name: file.originalname,
                size: file.size,
                mimeType: file.mimetype
            }));

            if (newFiles.length > 0) {
                // Use $push to add new files to the existing array
                updateObj.$push = { files: { $each: newFiles } };
            }
        }

        // Update timeline
        const updatedTimeline = await Timeline.findByIdAndUpdate(
            req.params.id,
            updateObj,
            { new: true }
        );

        if (!updatedTimeline) {
            return next(errorHandler(404, 'Timeline not found'));
        }

        res.status(200).json({
            success: true,
            message: 'Timeline updated successfully',
            timeline: updatedTimeline
        });
    } catch (error) {
        console.error('Update timeline error:', error);
        next(error);
    }
};

export const deleteTimeline = async (req, res, next) => {
    try {
        const timeline = await Timeline.findById(req.params.id);
        if (!timeline) {
            return next(errorHandler(404, 'Timeline not found'));
        }

        // Delete associated files
        if (timeline.files && timeline.files.length > 0) {
            const filePaths = timeline.files.map(file => file.path);
            await deleteFiles(filePaths);
        }

        await Timeline.findByIdAndDelete(req.params.id);
        res.status(200).json('Timeline has been deleted');
    } catch (error) {
        next(error);
    }
};

export const deleteTimelineFile = async (req, res, next) => {
    try {
        const { timelineId, fileId } = req.params;
        
        const timeline = await Timeline.findById(timelineId);
        if (!timeline) {
            return next(errorHandler(404, 'Timeline not found'));
        }

        // Find the file in the timeline's files array
        const fileToDelete = timeline.files.find(file => file._id.toString() === fileId);
        if (!fileToDelete) {
            return next(errorHandler(404, 'File not found'));
        }

        try {
            // Delete the file from storage
            await deleteFiles([fileToDelete.path]);
        } catch (deleteError) {
            console.error('Error deleting file from storage:', deleteError);
            // Continue with removing from database even if file deletion fails
        }

        // Remove the file from the timeline's files array
        timeline.files = timeline.files.filter(file => file._id.toString() !== fileId);
        await timeline.save();

        res.status(200).json({
            success: true,
            message: 'File has been deleted'
        });
    } catch (error) {
        console.error('Error in deleteTimelineFile:', error);
        next(error);
    }
}; 

export const getTimelines = async (req, res, next) => {
    try {
        const timelines = await Timeline.find(); // Fetch all timelines from the database
        res.status(200).json({ success: true, timelines });
    } catch (error) {
        console.error('Error fetching timelines:', error); // Log the error
        next(error);
    }
};