import express from 'express';
import { trackClick, getClickCounts } from '../controllers/click.controller.js';
import { verifyToken } from '../utils/verifyUser.js';

const router = express.Router();

// Track a click (protected - requires authentication)
router.post('/track', verifyToken, trackClick);

// Track a click (public - no authentication required)
router.post('/track/public', trackClick);

// Get all click counts (protected - requires authentication)
router.get('/counts', verifyToken, getClickCounts);

// Get all click counts (public - no authentication required)
router.get('/counts/public', getClickCounts);

export default router; 