import express from 'express';
import { getClickCountBy<PERSON><PERSON>, getAllClickCounts } from '../controllers/keymilestone.controller.js';
import { verifyToken } from '../utils/verifyUser.js';


const router = express.Router();

// Test route to check if the API is responding
router.get('/test', (req, res) => {
    res.status(200).json({
        success: true,
        message: 'Keymilestone API is working'
    });
});

// Get count in the new formatted response (doesn't increment counter)
router.get('/insert', verifyToken, (req, res, next) => {
    req.isReadOnly = true;
    getClickCountByKey(req, res, next);
});

// Increment click count (new endpoint)
router.post('/increment', (req, res, next) => {
    req.isReadOnly = false;
    getClickCountByKey(req, res, next);
});

// Get click count for a specific key without incrementing (full response)
router.get('/count', verifyToken, (req, res, next) => {
    req.isReadOnly = true;
    getClickCountByKey(req, res, next);
});

// Get just the count value without any JSON wrapping
router.get('/count/value', verifyToken, (req, res, next) => {
    req.isReadOnly = true;
    req.simpleResponse = true;
    getClickCountByKey(req, res, next);
});

// Direct routes for each key (returns just the count)
router.get('/count/1', verifyToken, (req, res, next) => {
    req.query.key = '1';
    req.isReadOnly = true;
    req.simpleResponse = true;
    getClickCountByKey(req, res, next);
});

router.get('/count/2', verifyToken, (req, res, next) => {
    req.query.key = '2';
    req.isReadOnly = true;
    req.simpleResponse = true;
    getClickCountByKey(req, res, next);
});

router.get('/count/3', verifyToken, (req, res, next) => {
    req.query.key = '3';
    req.isReadOnly = true;
    req.simpleResponse = true;
    getClickCountByKey(req, res, next);
});

router.get('/count/4', verifyToken, (req, res, next) => {
    req.query.key = '4';
    req.isReadOnly = true;
    req.simpleResponse = true;
    getClickCountByKey(req, res, next);
});

// Get all click counts
router.get('/all', verifyToken, getAllClickCounts);

// Create a new endpoint to actually increment the counter
router.post('/track', verifyToken, getClickCountByKey);

export default router; 