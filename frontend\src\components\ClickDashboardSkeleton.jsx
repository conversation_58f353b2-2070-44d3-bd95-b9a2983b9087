const ClickDashboardSkeleton = () => {
    return (
        <div className="bg-white rounded-lg shadow-lg p-6">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-gray-200 animate-pulse rounded" />
                    <div className="w-48 h-8 bg-gray-200 animate-pulse rounded" />
                </div>
                <div className="flex gap-2">
                    <div className="w-32 h-8 bg-gray-200 animate-pulse rounded" />
                    <div className="w-24 h-8 bg-gray-200 animate-pulse rounded" />
                </div>
            </div>

            {/* Cards Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {[...Array(4)].map((_, index) => (
                    <div key={index} className="bg-white rounded-lg border border-gray-200 p-4">
                        <div className="w-24 h-6 bg-gray-200 animate-pulse rounded-full mb-2" />
                        <div className="w-16 h-10 bg-gray-200 animate-pulse rounded mb-1" />
                        <div className="w-20 h-4 bg-gray-200 animate-pulse rounded" />
                    </div>
                ))}
            </div>
        </div>
    );
};

export default ClickDashboardSkeleton; 