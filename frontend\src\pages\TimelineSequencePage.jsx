import { useEffect, useState } from 'react';
import TimelineSequence from '../components/TimelineSequence';

const TimelineSequencePage = () => {
    const [timelines, setTimelines] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        fetchTimelines();
    }, []);

    const fetchTimelines = async () => {
        try {
            // const response = await fetch('/v2/timeline');
            const response = await fetch(`${import.meta.env.VITE_API_IP}/v2/timeline`);
            const data = await response.json();
            if (data.success) {
                // Sort timelines by year in ascending order
                const sortedTimelines = data.timelines.sort((a, b) => a.year - b.year);
                setTimelines(sortedTimelines);
            } else {
                setError(data.message);
            }
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-screen">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="text-center py-8">
                <p className="text-red-600 font-medium">{error}</p>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 py-12">
            <div className="max-w-4xl mx-auto px-4">
                <h1 className="text-3xl font-bold text-center mb-8">Our Timeline</h1>
                <TimelineSequence timelines={timelines} />
            </div>
        </div>
    );
};

export default TimelineSequencePage; 