import { useSelector, useDispatch } from "react-redux";
import { useState } from "react";
import {
  signOut,
  updateUserStart,
  updateUserSuccess,
  updateUserFailure,
} from "../redux/user/userSlice";
import { useNavigate } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEye, faEyeSlash } from "@fortawesome/free-solid-svg-icons";
import { post } from "../utils/api";

const Profile = () => {
  const { currentUser, loading, error } = useSelector((state) => state.user);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [formData, setFormData] = useState({});
  const [showSignOutConfirm, setShowSignOutConfirm] = useState(false);
  const [updateSuccess, setUpdateSuccess] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  //firebase storage code, put this in the firebase console
  // service firebase.storage {
  //   match /b/{bucket}/o {
  //     match /{allPaths=**} {
  //       allow read;
  //       allow write:if
  //       request.resource.size < 2 * 1024 * 1024 &&
  //       request.resource.contentType.matches('image/.*')
  //     }
  //   }
  // }

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.id]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      dispatch(updateUserStart());
      
      const res = await post(`${import.meta.env.VITE_API_IP}/v2/user/update/${currentUser._id}`, formData);
      
      const data = await res.json();
      if (data.success === false) {
        dispatch(updateUserFailure(data.message));
        return;
      }
      dispatch(updateUserSuccess(data));
      setUpdateSuccess(true);
    } catch (error) {
      dispatch(updateUserFailure(error.message));
    }
  };

  const handleSignOutClick = () => {
    setShowSignOutConfirm(true);
  };

  const handleSignOutConfirm = async () => {
    try {
      // const res = await post(`${import.meta.env.VITE_API_IP}/v2/auth/logout`, {});
      const res = await post(`${import.meta.env.VITE_API_IP}/auth/logout`, {});
      
      const data = await res.json();
      if (data.success === false) {
        console.log(data.message);
        return;
      }
      dispatch(signOut());
      navigate("/adminpanel/sign-in");
    } catch (error) {
      console.log(error);
    }
  };

  const handleSignOutCancel = () => {
    setShowSignOutConfirm(false);
  };

  const togglePasswordVisibility = () => {
    setShowPassword((prev) => !prev);
  };

  return (
    <div className="relative bg-gray-50 min-h-screen">
      {/* <h1 className="text-3xl font-semibold text-center my-7 text-gray-800">Profile</h1> */}
      <form
        onSubmit={handleSubmit}
        className="flex flex-col gap-6 max-w-md mx-auto bg-white p-6 rounded-lg shadow-md my-7"
      >
        <h1 className="text-3xl font-semibold text-center text-gray-800">
          Profile
        </h1>
        {/* <input
          onChange={(e) => setFile(e.target.files[0])}
          type="file"
          ref={fileRef}
          hidden
          accept="image/*"
        />
        <img
          onClick={() => fileRef.current.click()}
          src={formData.avatar || currentUser.user.avatar}
          alt="profile"
          className="h-24 w-24 object-cover rounded-full mx-auto cursor-pointer border-2 border-gray-300"
        />
        <p className="text-center text-sm">
          {fileUploadError ? (
            <span className="text-red-600">
              Error uploading image (image must be less than 2 mb)
            </span>
          ) : filePercent > 0 && filePercent < 100 ? (
            <span className="text-gray-600">{`Uploading ${filePercent}%`}</span>
          ) : filePercent === 100 ? (
            <span className="text-green-600">Image uploaded successfully</span>
          ) : (
            ""
          )}
        </p> */}
        <div className="flex flex-col gap-4">
          <div className="flex flex-col gap-1">
            <label
              htmlFor="username"
              className="text-sm font-medium text-gray-700"
            >
              Username
            </label>
            <input
              type="text"
              id="username"
              defaultValue={currentUser.user.username}
              className="bg-gray-100 outline-none p-3 rounded-lg cursor-not-allowed"
              disabled
            />
          </div>
          <div className="flex flex-col gap-1">
            <label
              htmlFor="email"
              className="text-sm font-medium text-gray-700"
            >
              Email
            </label>
            <input
              type="email"
              id="email"
              defaultValue={currentUser.user.email}
              className="bg-gray-100 outline-none p-3 rounded-lg cursor-not-allowed"
              disabled
            />
          </div>
          <div className="relative">
            <label
              htmlFor="password"
              className="text-sm font-medium text-gray-700"
            >
              Password
            </label>
            <input
              type={showPassword ? "text" : "password"}
              id="password"
              placeholder="New Password"
              className="bg-white outline-none p-3 rounded-lg w-full border border-gray-300"
              onChange={handleChange}
            />
            <button
              type="button"
              onClick={togglePasswordVisibility}
              className="absolute right-3 top-[50px] transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
            >
              <FontAwesomeIcon icon={showPassword ? faEyeSlash : faEye} />
            </button>
          </div>
        </div>

        <button
          disabled={loading}
          className="bg-blue-600 text-white p-3 rounded-lg uppercase hover:bg-blue-700 disabled:opacity-50"
        >
          {loading ? "Loading..." : "Update"}
        </button>

        {error && <p className="text-red-600 text-center">{error}</p>}

        {updateSuccess && (
          <p className="text-green-600 text-center">
            User is updated successfully!
          </p>
        )}
      </form>
      <div className="flex justify-between max-w-md mx-auto my-5 text-sm text-gray-700">
        <span className="cursor-pointer hover:text-red-600">
          Delete Account
        </span>
        <span
          onClick={handleSignOutClick}
          className="cursor-pointer hover:text-red-600"
        >
          Sign Out
        </span>
      </div>

      {/* Sign Out Confirmation Modal */}
      {showSignOutConfirm && (
        <div className="fixed inset-0 bg-blue bg-opacity-50 flex items-center justify-center">
          <div className="bg-white p-6 rounded-lg max-w-sm w-full mx-4 shadow-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-800">
              Sign Out Confirmation
            </h2>
            <p className="text-gray-600 mb-6">
              Are you sure you want to sign out?
            </p>
            <div className="flex justify-end gap-4">
              <button
                onClick={handleSignOutCancel}
                className="px-4 py-2 text-gray-500 hover:text-gray-700"
              >
                Cancel
              </button>
              <button
                onClick={handleSignOutConfirm}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Sign Out
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Profile;
