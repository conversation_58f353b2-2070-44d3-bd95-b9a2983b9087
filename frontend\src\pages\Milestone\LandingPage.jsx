/* eslint-disable react/prop-types */
/* eslint-disable no-unused-vars */
import React, { useRef, useState, useEffect } from 'react';
import { CircleLoader, WaveCircles } from "../../components/Milestone/LoadingAnimation";
import { motion } from 'framer-motion';

const LandingPage = ({ onStart }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [cacheBuster, setCacheBuster] = useState(Date.now()); // Unique value for cache-busting
  const [imageError, setImageError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const buttonRef = useRef(null);

  // Update cacheBuster on mount to force image reload
  useEffect(() => {
    setCacheBuster(Date.now());
  }, []);

  const handleImageLoad = () => {
    // console.log("Title video loaded successfully");
    setImageLoaded(true);
    setImageError(false);
  };

  const handleImageError = (e) => {
    console.error("Failed to load title video:", e);
    console.error("Attempted path:", `/milestone-v2/title-video.apng?cb=${cacheBuster}`);
    setImageError(true);
    setImageLoaded(false);
  };

  const handleClick = () => {
    setIsLoading(true);
    setTimeout(() => {
      onStart();
      setIsLoading(false);
    }, 10);
  };

  return (
    <div
      className="h-screen flex flex-col justify-center items-center text-white p-8 relative overflow-hidden"
      style={{
        background: 'radial-gradient(circle at 130% 10%, #23A3B1 50%, #0486AB 100%)',
      }}
    >
      {/* Start Button - Appears First */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 0.2, duration: 0.8, ease: 'easeOut' }}
        className="absolute bottom-10"
      >
        <div className="relative w-25 h-23">
          <WaveCircles />
          <button
            ref={buttonRef}
            className="w-24 h-22 rounded-full border-0 border-white transition-all flex items-center justify-center relative z-20"
            onClick={handleClick}
          >
            {isLoading ? (
              <CircleLoader />
            ) : (
              <span
                className="text-white text-2xl font-bold"
                style={{
                  fontFamily: "Prometo Xbold",
                  letterSpacing: "0px",
                }}
              >
                Start
              </span>
            )}
          </button>
        </div>
      </motion.div>

      {/* Logo with grayscale to white fade-in */}
      {imageError ? (
        <div className="w-905 h-905 flex items-center justify-center border-2 border-dashed border-white/50 rounded-lg">
          <div className="text-center">
            <p className="text-white/80 mb-2">Failed to load title video</p>
            <p className="text-white/60 text-sm">Path: /milestone-v2/title-video.apng</p>
          </div>
        </div>
      ) : (
        <img
          src={`/milestone-v2/title-video.apng?cb=${cacheBuster}`}
          alt="MISC Logo"
          className="w-905 h-905 object-contain"
          onLoad={handleImageLoad}
          onError={handleImageError}
          style={{ 
            opacity: imageLoaded ? 1 : 0.5,
            transition: 'opacity 0.3s ease'
          }}
        />
      )}
    </div>
  );
};

export default LandingPage;