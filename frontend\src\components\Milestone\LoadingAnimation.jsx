/* eslint-disable no-dupe-keys */
/* eslint-disable no-unused-vars */

import React, { useEffect, useState } from "react";

export const LoadingAnimation = () => {
  const [rotation, setRotation] = useState(0);
  
  useEffect(() => {
    const interval = setInterval(() => {
      setRotation(prev => (prev + 15) % 360);
    }, 50);
    
    return () => clearInterval(interval);
  }, []);
  
  return (
    <div className="relative w-8 h-8">
      {[0, 1, 2, 3, 4, 5].map((i) => (
        <div 
          key={i}
          className="absolute top-1/2 left-1/2 w-1.5 h-1.5 rounded-full bg-white opacity-0"
          style={{
            opacity: Math.max(0.2, 1 - (i * 0.15)),
            animationDelay: `${i * 0.15}s`,
            transformOrigin: 'center',
            transform: `rotate(${rotation}deg) translate(0, -6px) rotate(${(i * 60) % 360}deg)`,
          }}
        />
      ))}
    </div>
  );
};

// Alternative design - pulsing dots
export const PulsingDotsAnimation = () => {
  return (
    <div className="flex space-x-1">
      {[0, 1, 2].map((i) => (
        <div 
          key={i}
          className="w-2 h-2 bg-white rounded-full animate-pulse"
          style={{ 
            animationDelay: `${i * 0.15}s`,
            animationDuration: '5s',
          }}
        />
      ))}
    </div>
  );
};

// Alternative design - circle loader
export const CircleLoader = () => {
  return (
    <div className="w-6 h-6 border-2 border-white rounded-full border-t-transparent animate-spin" />
  );
};

export const WaveCircles = () => {
  return (
    <div className="absolute w-full h-full">
      <div style={{
        position: 'absolute',
        border: '1px solid white',
        borderRadius: '50%',
        opacity: 0.7,
        transformOrigin: 'center',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: '120%',
        height: '120%',
        animation: 'waveAnim1 7s ease-in-out infinite'
      }} />
      <div style={{
        position: 'absolute',
        border: '1px solid white',
        borderRadius: '50%',
        opacity: 0.7,
        transformOrigin: 'center',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: '130%',
        height: '130%',
        animation: 'waveAnim2 8s ease-in-out infinite'
      }} />
      <div style={{
        position: 'absolute',
        border: '1px solid white',
        borderRadius: '50%',
        opacity: 0.7,
        transformOrigin: 'center',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: '140%',
        height: '140%',
        animation: 'waveAnim3 9s ease-in-out infinite'
      }} />
      
      <style>
        {`
          @keyframes waveAnim1 {
            0% {
              transform: translate(-50%, -50%) scale(1) rotate(0deg);
            }
            50% {
              transform: translate(-52%, -52%) scale(1.3) rotate(180deg);
            }
            100% {
              transform: translate(-50%, -50%) scale(1) rotate(360deg);
            }
          }

          @keyframes waveAnim2 {
            0% {
              transform: translate(-50%, -50%) scale(1) rotate(0deg);
            }
            50% {
              transform: translate(-50%, -50%) scale(1.2) rotate(180deg);
            }
            100% {
              transform: translate(-50%, -50%) scale(1) rotate(360deg);
            }
          }

          @keyframes waveAnim3 {
            0% {
              transform: translate(-50%, -50%) scale(1) rotate(0deg);
            }
            50% {
              transform: translate(-48%, -48%) scale(0.9) rotate(180deg);
            }
            100% {
              transform: translate(-50%, -50%) scale(1) rotate(360deg);
            }
          }
        `}
      </style>
    </div>
  );
}; 