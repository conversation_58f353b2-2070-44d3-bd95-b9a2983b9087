import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { get, put, del } from '../utils/api';
import EditTimelineSkeleton from './EditTimelineSkeleton';

const EditTimeline = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const [formData, setFormData] = useState({
        year: '',
        key: '',
        title: '',
        description: '',
        caption: '',
    });
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [thumbnail, setThumbnail] = useState(null);
    const [thumbnailPreview, setThumbnailPreview] = useState(null);
    const [files, setFiles] = useState([]);
    const [newFiles, setNewFiles] = useState([]);

    // Add backend URL constant - using import.meta.env for Vite
    const BACKEND_URL = import.meta.env.VITE_API_IP || 'http://localhost:3000';

    useEffect(() => {
        const fetchTimeline = async () => {
            try {
                const response = await get(`${import.meta.env.VITE_API_IP}/v2/timeline/${id}`);
                const data = await response.json();
                
                if (data.success) {
                    setFormData({
                        year: data.timeline.year,
                        key: data.timeline.key,
                        title: data.timeline.title,
                        description: data.timeline.description,
                        caption: data.timeline.caption || '',
                    });
                    setThumbnail(data.timeline.thumbnail);
                    setFiles(data.timeline.files || []);
                } else {
                    setError(data.message);
                }
            } catch (err) {
                setError(err.message);
            } finally {
                setLoading(false);
            }
        };

        fetchTimeline();
    }, [id]);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleThumbnailChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            setThumbnail(file);
            setThumbnailPreview(URL.createObjectURL(file));
        }
    };

    const handleFilesChange = (e) => {
        const selectedFiles = Array.from(e.target.files);
        setNewFiles(prev => [...prev, ...selectedFiles]);
    };

    const handleRemoveNewFile = (index) => {
        setNewFiles(prev => prev.filter((_, i) => i !== index));
    };

    const handleRemoveExistingFile = async (fileId) => {
        try {
            const response = await del(`${import.meta.env.VITE_API_IP}/v2/timeline/${id}/files/${fileId}`);

            if (response.ok) {
                setFiles(prev => prev.filter(file => file._id !== fileId));
                toast.success('File removed successfully');
            } else {
                const data = await response.json();
                throw new Error(data.message || 'Failed to remove file');
            }
        } catch (err) {
            toast.error(err.message);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        try {
            const formDataToSend = new FormData();
            formDataToSend.append('year', formData.year);
            formDataToSend.append('key', formData.key);
            formDataToSend.append('title', formData.title);
            formDataToSend.append('description', formData.description);
            formDataToSend.append('caption', formData.caption);

            // Append new thumbnail if selected
            if (thumbnail) {
                formDataToSend.append('thumbnail', thumbnail);
            }

            // Append new files
            newFiles.forEach(file => {
                formDataToSend.append('files', file);
            });

            const response = await put(`${import.meta.env.VITE_API_IP}/v2/timeline/update/${id}`, formDataToSend);

            const data = await response.json();

            if (response.ok) {
                toast.success('Timeline updated successfully');
                navigate('/adminpanel/event');
            } else {
                throw new Error(data.message || 'Error updating timeline');
            }
        } catch (err) {
            toast.error(err.message);
        }
    };

    // Add cleanup for thumbnail preview URL
    useEffect(() => {
        return () => {
            if (thumbnailPreview) {
                URL.revokeObjectURL(thumbnailPreview);
            }
        };
    }, [thumbnailPreview]);

    if (loading) return <EditTimelineSkeleton />;

    if (error) return (
        <div className="text-center py-4">
            <p className="text-red-600 font-medium">{error}</p>
        </div>
    );

    return (
        <div className="max-w-2xl mx-auto p-6">
            <h1 className="text-2xl font-bold mb-6">Edit Event</h1>
            <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Year
                    </label>
                    <input
                        type="number"
                        name="year"
                        value={formData.year}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                    />
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Category
                    </label>
                    <select
                        name="key"
                        value={formData.key}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                    >
                        <option value="our_journey">Our Journey</option>
                        <option value="highlights">Highlights</option>
                        <option value="sustainability">Sustainability</option>
                        <option value="dark_days">Dark Days</option>
                    </select>
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Title
                    </label>
                    <input
                        type="text"
                        name="title"
                        value={formData.title}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                    />
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Description
                    </label>
                    <textarea
                        name="description"
                        value={formData.description}
                        onChange={handleChange}
                        rows="4"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                    ></textarea>
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Caption
                    </label>
                    <input
                        type="text"
                        name="caption"
                        value={formData.caption}
                        onChange={handleChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Thumbnail
                    </label>
                    <div className="flex items-center justify-center w-full">
                        {thumbnailPreview ? (
                            <div className="relative w-full">
                                <img
                                    src={thumbnailPreview}
                                    alt="New thumbnail preview"
                                    className="w-full h-48 object-cover rounded-lg"
                                />
                                <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-black bg-opacity-50 rounded-lg">
                                    <label className="cursor-pointer flex items-center justify-center px-4 py-2 bg-white rounded-md shadow-sm">
                                        <svg className="w-5 h-5 mr-2 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                        </svg>
                                        Change Image
                                        <input
                                            type="file"
                                            onChange={handleThumbnailChange}
                                            accept="image/*"
                                            className="hidden"
                                        />
                                    </label>
                                </div>
                            </div>
                        ) : thumbnail ? (
                            <div className="relative w-full">
                                <img
                                    src={`${BACKEND_URL}/uploads/${thumbnail}`}
                                    alt="Current thumbnail"
                                    className="w-full h-48 object-cover rounded-lg"
                                />
                                <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-black bg-opacity-50 rounded-lg">
                                    <label className="cursor-pointer flex items-center justify-center px-4 py-2 bg-white rounded-md shadow-sm">
                                        <svg className="w-5 h-5 mr-2 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                        </svg>
                                        Change Image
                                        <input
                                            type="file"
                                            onChange={handleThumbnailChange}
                                            accept="image/*"
                                            className="hidden"
                                        />
                                    </label>
                                </div>
                            </div>
                        ) : (
                            <label className="flex flex-col items-center justify-center w-full h-48 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-all">
                                <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                    <svg className="w-8 h-8 mb-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                                        <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                                    </svg>
                                    <p className="mb-2 text-sm text-gray-500"><span className="font-semibold">Click to upload thumbnail</span></p>
                                    <p className="text-xs text-gray-500">PNG, JPG or JPEG (MAX. 800x400px)</p>
                                </div>
                                <input
                                    type="file"
                                    onChange={handleThumbnailChange}
                                    accept="image/*"
                                    className="hidden"
                                />
                            </label>
                        )}
                    </div>
                </div>

                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Files
                    </label>
                    {/* Existing files */}
                    {files.length > 0 && (
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                            {files.map((file) => (
                                <div key={file._id} className="relative group">
                                    {file.type === 'image' ? (
                                        <img
                                            src={`${BACKEND_URL}/uploads/${file.path}`}
                                            alt={file.name}
                                            className="w-full h-32 object-cover rounded-lg"
                                        />
                                    ) : (
                                        <video
                                            src={`${BACKEND_URL}/uploads/${file.path}`}
                                            className="w-full h-32 object-cover rounded-lg"
                                            controls
                                        />
                                    )}
                                    <button
                                        type="button"
                                        onClick={() => handleRemoveExistingFile(file._id)}
                                        className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                        </svg>
                                    </button>
                                </div>
                            ))}
                        </div>
                    )}

                    {/* New files upload button */}
                    <div className="flex items-center justify-center w-full">
                        <label className="flex flex-col items-center justify-center w-full h-40 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-all">
                            <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                <svg className="w-8 h-8 mb-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                                </svg>
                                <p className="mb-2 text-sm text-gray-500"><span className="font-semibold">Click to add more files</span> or drag and drop</p>
                                <p className="text-xs text-gray-500">Images (PNG, JPG) or Videos (MP4, WebM)</p>
                                <p className="text-xs text-gray-500 mt-1">Maximum size per file: 50MB</p>
                            </div>
                            <input
                                type="file"
                                onChange={handleFilesChange}
                                multiple
                                accept="image/*,video/*"
                                className="hidden"
                            />
                        </label>
                    </div>
                </div>

                <div>
                    {newFiles.length > 0 && (
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mt-4">
                            {newFiles.map((file, index) => (
                                <div key={index} className="relative group">
                                    {file.type.startsWith('image/') ? (
                                        <img
                                            src={URL.createObjectURL(file)}
                                            alt={`New file ${index + 1}`}
                                            className="w-full h-32 object-cover rounded-lg"
                                        />
                                    ) : (
                                        <video
                                            src={URL.createObjectURL(file)}
                                            className="w-full h-32 object-cover rounded-lg"
                                            controls
                                        />
                                    )}
                                    <button
                                        type="button"
                                        onClick={() => handleRemoveNewFile(index)}
                                        className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                        </svg>
                                    </button>
                                </div>
                            ))}
                        </div>
                    )}
                </div>

                <div className="flex gap-4">
                    <button
                        type="submit"
                        className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        Update Timeline
                    </button>
                    <button
                        type="button"
                        onClick={() => navigate('/adminpanel/event')}
                        className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500"
                    >
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    );
};

export default EditTimeline; 