import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import SignIn from "./pages/SignIn";
import SignUp from "./pages/SignUp";
import Profile from "./pages/Profile";
import About from "./pages/About";
import Header from "./components/Header";
import PrivateRoute from "./components/PrivateRoute";
import CreateTimeline from "./pages/CreateTimeline";
import TimelineList from "./components/TimelineList";
import EditTimeline from "./components/EditTimeline";
// import TimelineSequencePage from "./pages/TimelineSequencePage";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import ClickAnalytics from "./pages/ClickAnalytics";
import Sidebar from "./components/Sidebar";
import { useSelector } from "react-redux";
import { Scene } from "./pages/Milestone/Scene";
import { Scene3 } from "./pages/Milestone/Scene3";

const App = () => {
  const { currentUser } = useSelector((state) => state.user);
  
  // Determine basename based on current path
  const getBasename = () => {
    const path = window.location.pathname;
    if (path.startsWith('/milestone-v2')) {
      return '/milestone-v2';
    }
    return '';
  };

  return (
    <Router basename={getBasename()}>
      <Routes>
        {/* Default route - shows main milestone content */}
        <Route path="/" element={<Scene3 />} />
        
        {/* Alternative milestone route */}
        <Route path="/milestone3" element={<Scene />} />

        {/* Admin Panel routes */}
        <Route path="/adminpanel/*" element={
          <div className="flex min-h-screen">
            {currentUser && <Sidebar />}
            <div className={`flex-1 ${currentUser ? 'ml-64' : ''}`}>
              <Header />
              <main className="p-6">
                <Routes>
                  <Route index element={currentUser ? <TimelineList /> : <SignIn />} />
                  <Route 
                    path="sign-in" 
                    element={currentUser ? <Navigate to="/adminpanel" replace /> : <SignIn />} 
                  />
                  <Route 
                    path="sign-up" 
                    element={currentUser ? <Navigate to="/adminpanel" replace /> : <SignUp />} 
                  />
                  <Route path="about" element={<About />} />
                  {/* <Route path="/event-sequence" element={<TimelineSequencePage />} /> */}
                  <Route element={<PrivateRoute />}>
                    <Route path="profile" element={<Profile />} />
                    <Route path="create-event" element={<CreateTimeline />} />
                    <Route path="event" element={<TimelineList />} />
                    <Route path="timeline/edit/:id" element={<EditTimeline />} />
                    <Route path="analytics" element={<ClickAnalytics />} />
                  </Route>
                </Routes>
              </main>
            </div>
          </div>
        } />
      </Routes>
      <ToastContainer position="bottom-right" />
    </Router>
  );
};

export default App;