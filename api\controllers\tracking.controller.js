import Tracking from '../models/tracking.model.js';
import { errorHandler } from '../utils/error.js';

export const getTrackingStatus = async (req, res, next) => {
    try {
        let tracking = await Tracking.findOne();
        
        if (!tracking) {
            tracking = await Tracking.create({});
        }

        res.status(200).json({
            success: true,
            isEnabled: tracking.isEnabled
        });
    } catch (error) {
        next(error);
    }
};

export const updateTrackingStatus = async (req, res, next) => {
    try {
        const { isEnabled } = req.body;
        
        let tracking = await Tracking.findOne();
        
        if (!tracking) {
            tracking = await Tracking.create({ isEnabled });
        } else {
            tracking.isEnabled = isEnabled;
            await tracking.save();
        }

        res.status(200).json({
            success: true,
            isEnabled: tracking.isEnabled
        });
    } catch (error) {
        next(error);
    }
}; 